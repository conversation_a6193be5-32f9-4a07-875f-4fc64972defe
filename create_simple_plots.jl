#!/usr/bin/env julia

"""
Simple Scientific Visualization Script for LIDAR Data
Creates basic plots from processed LIDAR data
"""

using CSV, DataFrames, Plots, Statistics

println("🎯 SIMPLE LIDAR DATA VISUALIZATION")
println("=" ^ 50)

# Set plot backend
gr()

# Create output directory for plots
plots_dir = "scientific_plots"
if !isdir(plots_dir)
    mkpath(plots_dir)
    println("📁 Created plots directory: $plots_dir")
end

"""
Create a simple altitude profile plot from a single file
"""
function create_simple_altitude_plot()
    println("\n📊 Creating simple altitude profile plot...")
    
    # Find the first AOD file
    aod_files = filter(f -> endswith(f, "_aod.csv"), readdir("data", join=true))
    
    if isempty(aod_files)
        println("❌ No AOD files found")
        return nothing
    end
    
    # Use the first file
    filename = aod_files[1]
    println("📋 Using file: $(basename(filename))")
    
    try
        # Read the data
        df = CSV.read(filename, DataFrame)
        
        # Create altitude profile plot
        p = plot(title="LIDAR Altitude Profile", 
                xlabel="Signal Intensity", 
                ylabel="Range (m)",
                size=(800, 600))
        
        # Plot different parameters
        if "Corrected_Counts" in names(df) && "Range_m" in names(df)
            plot!(p, df.Corrected_Counts[1:10:end], df.Range_m[1:10:end], 
                  label="Corrected Counts", linewidth=2, alpha=0.8)
        end
        
        if "Range_Corrected" in names(df)
            plot!(p, df.Range_Corrected[1:10:end], df.Range_m[1:10:end], 
                  label="Range Corrected", linewidth=2, alpha=0.8)
        end
        
        if "Normalized" in names(df)
            plot!(p, df.Normalized[1:10:end], df.Range_m[1:10:end], 
                  label="Normalized", linewidth=2, alpha=0.8)
        end
        
        # Save plot
        output_file = "$plots_dir/simple_altitude_profile.png"
        savefig(p, output_file)
        println("  💾 Saved: $output_file")
        
        return p
        
    catch e
        println("  ❌ Error: $e")
        return nothing
    end
end

"""
Create ASR plot
"""
function create_asr_plot()
    println("\n📈 Creating ASR plot...")
    
    # Find ASR files
    asr_files = filter(f -> endswith(f, "_asr.csv"), readdir("data", join=true))
    
    if isempty(asr_files)
        println("❌ No ASR files found")
        return nothing
    end
    
    # Use the first file
    filename = asr_files[1]
    println("📋 Using file: $(basename(filename))")
    
    try
        # Read the data
        df = CSV.read(filename, DataFrame)
        
        # Create ASR plot
        p = plot(title="Aerosol Scattering Ratio (ASR)", 
                xlabel="ASR", 
                ylabel="Range (m)",
                size=(600, 600))
        
        if "ASR" in names(df) && "Range_m" in names(df)
            plot!(p, df.ASR[1:10:end], df.Range_m[1:10:end], 
                  label="ASR Profile", color=:red, linewidth=2)
            
            # Add reference line at ASR = 1
            vline!(p, [1.0], label="Molecular Reference", 
                   linestyle=:dash, color=:black, linewidth=2)
        end
        
        # Save plot
        output_file = "$plots_dir/asr_profile.png"
        savefig(p, output_file)
        println("  💾 Saved: $output_file")
        
        return p
        
    catch e
        println("  ❌ Error: $e")
        return nothing
    end
end

"""
Create AOD time series plot
"""
function create_aod_time_series()
    println("\n⏰ Creating AOD time series...")
    
    # Find AOD files
    aod_files = filter(f -> endswith(f, "_aod.csv"), readdir("data", join=true))
    
    if length(aod_files) < 5
        println("❌ Not enough AOD files for time series")
        return nothing
    end
    
    # Extract total AOD values
    total_aods = Float64[]
    filenames = String[]
    
    for file in aod_files[1:min(50, length(aod_files))]  # Limit to 50 files
        try
            df = CSV.read(file, DataFrame)
            if nrow(df) > 0 && "AOD_profile" in names(df)
                # Get total AOD (last value in profile)
                total_aod = df.AOD_profile[end]
                if !isnan(total_aod) && isfinite(total_aod)
                    push!(total_aods, total_aod)
                    push!(filenames, basename(file))
                end
            end
        catch e
            println("  ⚠️  Error processing $file: $e")
        end
    end
    
    if length(total_aods) < 3
        println("❌ Not enough valid AOD data")
        return nothing
    end
    
    # Create time series plot
    p = plot(1:length(total_aods), total_aods, 
             title="Total AOD Time Series", 
             xlabel="File Index", 
             ylabel="Total AOD",
             marker=:circle, 
             linewidth=2, 
             markersize=4,
             label="Total AOD",
             size=(800, 400))
    
    # Add statistics
    mean_aod = mean(total_aods)
    std_aod = std(total_aods)
    
    hline!(p, [mean_aod], label="Mean AOD", 
           linestyle=:dash, color=:red, linewidth=2)
    
    # Save plot
    output_file = "$plots_dir/aod_time_series.png"
    savefig(p, output_file)
    println("  💾 Saved: $output_file")
    
    # Print statistics
    println("  📊 AOD Statistics:")
    println("     Files processed: $(length(total_aods))")
    println("     Mean: $(round(mean_aod, digits=6))")
    println("     Std:  $(round(std_aod, digits=6))")
    println("     Min:  $(round(minimum(total_aods), digits=6))")
    println("     Max:  $(round(maximum(total_aods), digits=6))")
    
    return p
end

"""
Create backscatter comparison plot
"""
function create_backscatter_plot()
    println("\n🔬 Creating backscatter comparison plot...")
    
    # Find backscatter files
    back_files = filter(f -> endswith(f, "_backscatter.csv"), readdir("data", join=true))
    
    if isempty(back_files)
        println("❌ No backscatter files found")
        return nothing
    end
    
    # Use the first file
    filename = back_files[1]
    println("📋 Using file: $(basename(filename))")
    
    try
        # Read the data
        df = CSV.read(filename, DataFrame)
        
        # Create backscatter plot
        p = plot(title="Backscatter Coefficient Comparison", 
                xlabel="Backscatter Coefficient (m⁻¹sr⁻¹)", 
                ylabel="Range (m)",
                size=(800, 600))
        
        if "Beta_molecular" in names(df) && "Range_m" in names(df)
            plot!(p, df.Beta_molecular[1:10:end], df.Range_m[1:10:end], 
                  label="Molecular Backscatter", color=:blue, linewidth=2)
        end
        
        if "Beta_total" in names(df)
            plot!(p, df.Beta_total[1:10:end], df.Range_m[1:10:end], 
                  label="Total Backscatter", color=:green, linewidth=2)
        end
        
        if "Beta_aerosol" in names(df)
            plot!(p, df.Beta_aerosol[1:10:end], df.Range_m[1:10:end], 
                  label="Aerosol Backscatter", color=:red, linewidth=2)
        end
        
        # Save plot
        output_file = "$plots_dir/backscatter_comparison.png"
        savefig(p, output_file)
        println("  💾 Saved: $output_file")
        
        return p
        
    catch e
        println("  ❌ Error: $e")
        return nothing
    end
end

"""
Main function
"""
function main()
    println("\n🚀 Starting simple LIDAR visualization...")
    
    # Create plots
    create_simple_altitude_plot()
    create_asr_plot()
    create_aod_time_series()
    create_backscatter_plot()
    
    println("\n✅ Simple visualization complete!")
    println("📁 All plots saved in: $plots_dir/")
    
    # List generated plots
    plot_files = filter(f -> endswith(f, ".png"), readdir(plots_dir))
    println("\n📊 Generated plots:")
    for file in plot_files
        println("   • $file")
    end
end

# Run the analysis
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
