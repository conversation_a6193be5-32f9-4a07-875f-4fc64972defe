# LIDAR Processing Pipeline - Simplified Version
# Clean and error-free implementation

# Include all modules
include("modules/licel_raw_reader.jl")
include("modules/noise_correction.jl")
include("modules/range_correction.jl")
include("modules/normalization.jl")
include("modules/molecular_backscatter.jl")
include("modules/backscatter_analysis.jl")
include("modules/asr_analysis.jl")
include("modules/aod_calculation.jl")
include("modules/rtdi_generator.jl")

"""
    run_complete_pipeline(input_dir::String="data", output_dir::String="data") -> Vector{String}

Run the complete LIDAR processing pipeline.
"""
function run_complete_pipeline(input_dir::String="data", output_dir::String="data")
    println("="^60)
    println("COMPLETE LIDAR PROCESSING PIPELINE")
    println("="^60)
    
    # Step 1: Raw data conversion (if needed)
    if !isdir(input_dir) || isempty(readdir(input_dir))
        println("📁 Converting raw LICEL files to CSV...")
        batch_process_clear_sky_data()
    end
    
    # Step 2: Noise correction
    println("\n🔧 Step 1: Noise Correction")
    noise_files = batch_process_noise_correction(input_dir, output_dir)
    
    # Step 3: Range correction
    println("\n🔧 Step 2: Range Correction")
    range_files = batch_process_range_correction(output_dir, output_dir)
    
    # Step 4: Normalization
    println("\n🔧 Step 3: Normalization")
    norm_files = batch_process_normalization(output_dir, output_dir)
    
    # Step 5: Molecular backscatter
    println("\n🔧 Step 4: Molecular Backscatter")
    mol_files = batch_process_molecular_backscatter(output_dir, output_dir)
    
    # Step 6: Backscatter analysis
    println("\n🔧 Step 5: Backscatter Analysis")
    back_files = batch_process_backscatter_analysis(output_dir, output_dir)
    
    # Step 7: ASR analysis
    println("\n🔧 Step 6: ASR Analysis")
    asr_files = batch_process_asr_analysis(output_dir, output_dir)
    
    # Step 8: AOD calculation
    println("\n🔧 Step 7: AOD Calculation")
    aod_files = batch_process_aod_calculation(output_dir, output_dir)
    
    println("\n" * "="^60)
    println("PIPELINE COMPLETE")
    println("Final processed files: $(length(aod_files))")
    println("="^60)
    
    return aod_files
end

"""
    demo_single_file() -> String

Process a single file for demonstration.
"""
function demo_single_file()
    println("="^60)
    println("SINGLE FILE DEMO")
    println("="^60)
    
    # Find first available LICEL file
    test_folders = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
        "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
    ]
    
    test_file = ""
    for folder in test_folders
        if isdir(folder)
            files = find_licel_files(folder)
            if !isempty(files)
                test_file = files[1]
                break
            end
        end
    end
    
    if isempty(test_file)
        println("❌ No LICEL files found for demo")
        return ""
    end
    
    println("📁 Processing demo file: $(basename(test_file))")
    
    try
        # Step 1: Read raw file
        data = read_licel_raw_file(test_file)
        println("✅ Raw data loaded: $(length(data.raw_counts)) points")
        
        # Step 2: Save as CSV
        demo_csv = "demo_file.csv"
        save_lidar_csv(data, demo_csv)
        
        # Step 3: Process through pipeline
        noise_csv = process_noise_correction_csv(demo_csv)
        range_csv = process_range_correction_csv(noise_csv)
        norm_csv = process_normalization_csv(range_csv)
        mol_csv = process_molecular_backscatter_csv(norm_csv)
        back_csv = process_backscatter_analysis_csv(mol_csv)
        asr_csv = process_asr_analysis_csv(back_csv)
        final_csv = process_aod_calculation_csv(asr_csv)
        
        println("✅ Demo complete! Final file: $final_csv")
        return final_csv
        
    catch e
        println("❌ Demo failed: $e")
        return ""
    end
end

"""
    create_rtdi_demo() -> Matrix{UInt32}

Create RTDI map demo.
"""
function create_rtdi_demo()
    println("="^60)
    println("RTDI GENERATION DEMO")
    println("="^60)
    
    # Find first available folder
    test_folders = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
        "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
    ]
    
    for folder in test_folders
        if isdir(folder)
            println("📁 Creating RTDI map from: $folder")
            rtdi_data = create_rtdi_map_from_raw_files(folder, 100, 10)
            
            # Save heatmap
            save_rtdi_heatmap(rtdi_data, "rtdi_demo.png", title="RTDI Demo")
            
            println("✅ RTDI demo complete!")
            return rtdi_data
        end
    end
    
    println("❌ No folders found for RTDI demo")
    return zeros(UInt32, 0, 0)
end

# Main help function
function show_help()
    println("="^60)
    println("SIMPLIFIED LIDAR PROCESSING SYSTEM")
    println("="^60)
    println()
    println("🚀 MAIN FUNCTIONS:")
    println("  demo_single_file()           - Process one file demo")
    println("  create_rtdi_demo()           - Create RTDI map demo")
    println("  run_complete_pipeline()      - Process all data")
    println("  batch_process_clear_sky_data() - Convert raw files to CSV")
    println()
    println("📊 INDIVIDUAL PROCESSING:")
    println("  process_noise_correction_csv(file)")
    println("  process_range_correction_csv(file)")
    println("  process_normalization_csv(file)")
    println("  process_molecular_backscatter_csv(file)")
    println("  process_backscatter_analysis_csv(file)")
    println("  process_asr_analysis_csv(file)")
    println("  process_aod_calculation_csv(file)")
    println()
    println("🎯 RTDI FUNCTIONS:")
    println("  create_rtdi_map_from_raw_files(folder, max_range, max_files)")
    println("  save_rtdi_heatmap(rtdi_data, output_path)")
    println("  batch_create_rtdi_maps(input_folder, output_folder)")
    println()
    println("="^60)
end

# Show help on load
show_help()
