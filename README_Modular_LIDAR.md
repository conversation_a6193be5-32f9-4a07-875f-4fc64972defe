# Modular LIDAR Data Processing System

A comprehensive, modular Julia-based system for processing LICEL LIDAR data with UInt32 photon count integrity for RTDI (Range-Time-Display-Intensity) consistency.

## 🏗️ System Architecture

### Modular Design
Each processing stage is implemented as a separate Julia module, allowing for:
- **Independent testing** of each processing step
- **Flexible pipeline configuration** 
- **Easy maintenance and updates**
- **Traceable data flow** with CSV augmentation at each stage

### Data Flow
```
Raw LICEL Files → CSV → Noise Corrected → Range Corrected → Normalized → Molecular → Backscatter → ASR → AOD + RTDI
```

## 📁 Module Structure

```
modules/
├── licel_raw_reader.jl       # Raw LICEL file processing → CSV
├── noise_correction.jl       # Noise estimation & removal (36-60km)
├── range_correction.jl       # 1/z² range correction (30m resolution)
├── normalization.jl          # Reference region normalization (8-12km)
├── molecular_backscatter.jl  # βₘ(z) calculation using standard atmosphere
├── backscatter_analysis.jl   # Total & aerosol backscatter coefficients
├── asr_analysis.jl           # Aerosol Scattering Ratio (ASR) calculation
├── aod_calculation.jl        # Aerosol Optical Depth (AOD) analysis
└── rtdi_generator.jl         # RTDI map generation and visualization
```

## 🔧 Key Features

### UInt32 Photon Count Integrity
- **Preserves original data format** from LICEL files
- **Overflow detection** for values > Int32 max (2,147,483,647)
- **Maintains RTDI consistency** with previous working implementations

### CSV-Based Data Pipeline
- **Metadata preservation** in CSV headers
- **Column augmentation** at each processing stage
- **Traceable processing history** in file metadata
- **Easy data inspection** and validation

### Comprehensive Visualization
- **Before/after plots** for each processing stage
- **Physical validation plots** (atmospheric profiles, backscatter coefficients)
- **ASR and AOD analysis plots** with interpretation guidelines
- **RTDI maps** for temporal-spatial analysis
- **Automatic plot generation** with customizable output paths

### Advanced Analysis Capabilities
- **Aerosol Scattering Ratio (ASR)**: Atmospheric condition classification
- **Aerosol Optical Depth (AOD)**: Column and profile analysis with optional lidar ratio
- **RTDI Map Generation**: Range-Time-Display-Intensity visualization for multiple processing stages

## 🚀 Quick Start

### 1. Load the System
```julia
include("lidar_processing_pipeline.jl")
```

### 2. Test System Integrity
```julia
include("test_modular_system.jl")
```

### 3. Process Single File (Demo)
```julia
demo_single_file()
```

### 4. Process All Clear Sky Data (Complete Analysis)
```julia
run_complete_pipeline()
```

This will process all data through the complete pipeline including:
- Raw data conversion and noise correction
- Range correction and normalization
- Molecular and aerosol backscatter calculation
- ASR (Aerosol Scattering Ratio) analysis
- AOD (Aerosol Optical Depth) calculation
- RTDI map generation for all processing stages

### 5. Advanced Analysis Functions
```julia
# Individual analysis modules
process_asr_analysis_csv("data/file_backscatter.csv")
process_aod_analysis_csv("data/file_backscatter.csv")
generate_rtdi_maps_for_day("data/")

# Batch processing
batch_process_asr_analysis("data/")
batch_process_aod_analysis("data/")
batch_generate_rtdi_maps(["data/day1/", "data/day2/"])
```

## 📊 Processing Stages

### Stage 1: Raw Data Conversion
**Module:** `licel_raw_reader.jl`
- Reads LICEL binary files with 4-line ASCII headers
- Converts UInt32 photon counts to CSV format
- Preserves exact numerical values for RTDI consistency
- **Output:** `filename.csv` with columns: Index, Counts, Photon_Counts, Altitude_m, Range_m, Bin_Center_m

### Stage 2: Noise Correction
**Module:** `noise_correction.jl`
- Estimates noise using 36-60km altitude range (aerosol-free region)
- Applies correction: S_clean(z) = S_raw(z) - Noise
- **Adds columns:** Noise_Level, S_clean, Noise_Indices

### Stage 3: Range Correction
**Module:** `range_correction.jl`
- Applies 1/z² correction: X²(z) = S_clean(z) × z²
- Compensates for signal attenuation with distance
- **Adds columns:** X2, Range_Enhancement_Factor

### Stage 4: Normalization
**Module:** `normalization.jl`
- Selects clean reference region (8-12km altitude)
- Normalizes: Xⁿ²(z) = X²(z) / X²_ref
- **Adds columns:** X2_normalized, Reference_Value, Reference_Indices, Unity_Deviation

### Stage 5: Molecular Backscatter
**Module:** `molecular_backscatter.jl`
- Calculates βₘ(z) using standard atmosphere models
- Includes pressure and temperature profiles
- **Adds columns:** Beta_molecular, Pressure_Pa, Temperature_K, Beta_molecular_1e6

### Stage 6: Backscatter Analysis
**Module:** `backscatter_analysis.jl`
- Calculates total backscatter: βₜₒₜₐₗ(z) ∝ Xⁿ²(z)
- Derives aerosol backscatter: βₐ(z) = βₜₒₜₐₗ(z) - βₘ(z)
- **Adds columns:** Beta_total, Beta_aerosol, Beta_total_1e6, Beta_aerosol_1e6, Aerosol_Molecular_Ratio

## 📂 Directory Structure

```
NARL/
├── modules/                    # Processing modules
├── data/                      # CSV data files (all processing stages)
├── plots/                     # Visualization outputs
├── results/                   # Analysis results
├── clear sky CBL Aratrika/    # Raw LICEL data folders
│   ├── 08May2023_CBL_clearsky/
│   ├── 10May2023_CBL_clear Sky/
│   ├── 16May2023_CBL_study_clear_sky/
│   └── 18May2023_CBL_study_clearsky/
├── lidar_processing_pipeline.jl  # Main orchestrator
├── test_modular_system.jl        # System test script
└── README_Modular_LIDAR.md       # This documentation
```

## 🔍 Data Validation

### Overflow Detection
```julia
# Check for UInt32 values > Int32 max
overflow_analysis = check_overflow_values(raw_counts)
```

### Physical Consistency
- Molecular backscatter values: 1e-8 to 1e-4 m⁻¹ sr⁻¹
- Normalized signal unity deviation < 0.1 in reference region
- Non-negative aerosol backscatter coefficients

### Processing Validation
- Before/after comparison plots for each stage
- Metadata tracking in CSV headers
- Statistical summaries in processing logs

## 🎯 RTDI Consistency Features

### UInt32 Data Preservation
- Direct reading of LICEL binary data as UInt32
- No premature conversion to signed integers
- Maintains exact photon count values

### Reference Implementation Compatibility
- Follows established LIDAR processing conventions
- Preserves calibration constants and reference values
- Compatible with existing RTDI map generation

### Traceable Processing
- Each CSV contains complete processing history
- Intermediate results preserved for validation
- Metadata includes all processing parameters

## 🛠️ Advanced Usage

### Individual Module Processing
```julia
# Process only specific stages
include("modules/noise_correction.jl")
noise_corrected_csv = process_noise_correction_csv("data/sample.csv")
```

### Batch Processing by Stage
```julia
# Process all files through noise correction
batch_process_noise_correction("data", "data")
```

### Custom Parameters
```julia
# Custom altitude ranges
process_normalization_csv("input.csv", "output.csv", 
                         ref_min_alt=6000.0, ref_max_alt=10000.0)
```

## 📈 Output Files

### CSV Data Files
- **Raw:** `filename.csv`
- **Noise Corrected:** `filename_noise_corrected.csv`
- **Range Corrected:** `filename_range_corrected.csv`
- **Normalized:** `filename_normalized.csv`
- **Molecular:** `filename_molecular.csv`
- **Backscatter:** `filename_backscatter.csv`

### Visualization Plots
- **Noise Correction:** Before/after signal comparison
- **Range Correction:** Signal enhancement visualization
- **Normalization:** Reference region analysis
- **Molecular Backscatter:** Atmospheric profiles
- **Backscatter Analysis:** Coefficient comparisons

## 🔧 System Requirements

- **Julia 1.6+**
- **Required Packages:** CSV, DataFrames, Statistics, Plots, Dates, Printf
- **Memory:** Sufficient for processing large LICEL files
- **Storage:** Space for CSV files and plots (typically 2-3x original data size)

## 🚨 Troubleshooting

### Common Issues
1. **Module not found:** Ensure all files are in correct directories
2. **CSV column missing:** Run previous processing stages first
3. **Overflow warnings:** Normal for LICEL data, indicates proper UInt32 handling
4. **Plot display issues:** Check Plots.jl backend configuration

### Validation Steps
1. Run `test_modular_system.jl` to verify system integrity
2. Check CSV metadata headers for processing history
3. Validate physical ranges in processing logs
4. Compare plots with expected atmospheric patterns

This modular system ensures robust, traceable, and RTDI-consistent processing of LICEL LIDAR data while maintaining the flexibility to process individual stages or complete datasets.
