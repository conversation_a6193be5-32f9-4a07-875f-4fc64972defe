# Normalization Module - Simplified Version

using CSV
using DataFrames
using Statistics

"""
    normalize_to_reference_region(range_corrected::Vector{Float64}, range::Vector{Float64}) -> Vector{Float64}

Normalize to reference region (3-6 km).
"""
function normalize_to_reference_region(range_corrected::Vector{Float64}, range::Vector{Float64})
    ref_indices = findall(r -> 3000.0 <= r <= 6000.0, range)
    
    if isempty(ref_indices)
        println("⚠️  No data in reference region (3-6 km), using first 10% of data")
        ref_indices = 1:max(1, round(Int, 0.1 * length(range_corrected)))
    end
    
    ref_value = mean(range_corrected[ref_indices])
    
    if ref_value <= 0
        println("⚠️  Reference value is zero or negative, using 1.0")
        ref_value = 1.0
    end
    
    normalized = range_corrected ./ ref_value
    
    println("📊 Normalized to reference region ($(length(ref_indices)) points, ref_value: $(ref_value:.2e))")
    
    return normalized
end

"""
    process_normalization_csv(input_csv::String, output_csv::String="") -> String

Process normalization for CSV file.
"""
function process_normalization_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_normalized.csv")
    end
    
    println("Processing normalization: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Range_Corrected" in names(df)) || !("Range_m" in names(df))
        error("Required columns not found. Run range correction first.")
    end
    
    range_corrected = df.Range_Corrected
    range = df.Range_m
    
    normalized = normalize_to_reference_region(range_corrected, range)
    
    # Add normalized data
    df.Normalized = normalized
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Normalized data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_normalization(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process normalization.
"""
function batch_process_normalization(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing normalization...")
    
    csv_files = filter(file -> endswith(file, "_range_corrected.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No range corrected CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_range_corrected.csv" => "_normalized.csv"))
            result_file = process_normalization_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export normalize_to_reference_region
export process_normalization_csv, batch_process_normalization
