# Normalization Module
# Reference region normalization (8-12km clean atmosphere)
# Augments CSV data with normalized signal columns

using CSV
using DataFrames
using Statistics
using Plots

# Configuration constants
const REF_ALTITUDE_MIN = 8000.0   # 8 km
const REF_ALTITUDE_MAX = 12000.0  # 12 km

"""
    select_reference_region(altitude::Vector{Float64}, range_corrected::Vector{Float64};
                           min_alt::Float64=REF_ALTITUDE_MIN,
                           max_alt::Float64=REF_ALTITUDE_MAX) -> Tuple{Vector{Int}, Float64}

Select a clean reference region in the atmosphere for normalization.
Returns reference indices and reference value.
"""
function select_reference_region(altitude::Vector{Float64}, range_corrected::Vector{Float64};
                                min_alt::Float64=REF_ALTITUDE_MIN,
                                max_alt::Float64=REF_ALTITUDE_MAX)
    
    # Find indices in the reference altitude range
    ref_indices = findall(alt -> min_alt <= alt <= max_alt, altitude)
    
    if isempty(ref_indices)
        @warn "No data points found in reference region ($min_alt - $max_alt m)"
        # Use middle 20% of data as fallback
        n_points = length(altitude)
        start_idx = round(Int, 0.4 * n_points)
        end_idx = round(Int, 0.6 * n_points)
        ref_indices = start_idx:end_idx
    end
    
    # Calculate reference value as mean in this region
    ref_signal = range_corrected[ref_indices]
    ref_value = mean(ref_signal)
    
    println("Reference region selection:")
    println("  Altitude range: $(minimum(altitude[ref_indices]):.1f) - $(maximum(altitude[ref_indices]):.1f) m")
    println("  Number of points: $(length(ref_indices))")
    println("  Reference value (X²_ref): $(ref_value:.2e)")
    println("  Reference std: $(std(ref_signal):.2e)")
    
    return ref_indices, ref_value
end

"""
    normalize_signal(range_corrected::Vector{Float64}, ref_value::Float64) -> Vector{Float64}

Normalize the range-corrected signal: Xⁿ²(z) = X²(z) / X²_ref
"""
function normalize_signal(range_corrected::Vector{Float64}, ref_value::Float64)
    if ref_value <= 0
        error("Reference value must be positive")
    end
    
    normalized = range_corrected ./ ref_value
    
    return normalized
end

"""
    process_normalization_csv(input_csv::String, output_csv::String="";
                              ref_min_alt::Float64=REF_ALTITUDE_MIN,
                              ref_max_alt::Float64=REF_ALTITUDE_MAX,
                              plot_results::Bool=true, save_plots::Bool=true) -> String

Process signal normalization on CSV data and augment with new columns.
"""
function process_normalization_csv(input_csv::String, output_csv::String="";
                                  ref_min_alt::Float64=REF_ALTITUDE_MIN,
                                  ref_max_alt::Float64=REF_ALTITUDE_MAX,
                                  plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_normalized.csv")
    end
    
    println("Processing normalization for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["X2", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV. Run range correction first.")
        end
    end
    
    # Extract data
    range_corrected = df.X2
    altitude = df.Altitude_m
    
    # Select reference region
    ref_indices, ref_value = select_reference_region(altitude, range_corrected,
                                                   min_alt=ref_min_alt, max_alt=ref_max_alt)
    
    # Normalize signal
    normalized = normalize_signal(range_corrected, ref_value)
    
    println("Signal normalization completed:")
    println("  Normalized signal range: $(minimum(normalized):.3f) - $(maximum(normalized):.3f)")
    println("  Unity deviation in ref region: $(abs(mean(normalized[ref_indices]) - 1.0):.4f)")
    
    # Add new columns to DataFrame
    df.X2_normalized = normalized
    df.Reference_Value = fill(ref_value, nrow(df))
    df.Reference_Indices = [i in ref_indices for i in 1:nrow(df)]
    df.Unity_Deviation = abs.(normalized .- 1.0)
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add normalization metadata
    push!(metadata_lines, "# Signal Normalization Applied")
    push!(metadata_lines, "# Reference Altitude Range: $(ref_min_alt) - $(ref_max_alt) m")
    push!(metadata_lines, "# Reference Value: $(ref_value:.2e)")
    push!(metadata_lines, "# Reference Points: $(length(ref_indices))")
    push!(metadata_lines, "# Unity Deviation: $(abs(mean(normalized[ref_indices]) - 1.0):.4f)")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ Normalized CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_normalization.png")
            mkpath("plots")
        end
        
        create_normalization_plot(range_corrected, normalized, altitude, ref_value, 
                                ref_indices, ref_min_alt, ref_max_alt, plot_filename)
    end
    
    return output_csv
end

"""
    create_normalization_plot(range_corrected::Vector{Float64}, normalized::Vector{Float64},
                             altitude::Vector{Float64}, ref_value::Float64,
                             ref_indices::Vector{Int}, ref_min_alt::Float64, ref_max_alt::Float64,
                             output_path::String="")

Create before/after comparison plot for normalization.
"""
function create_normalization_plot(range_corrected::Vector{Float64}, normalized::Vector{Float64},
                                  altitude::Vector{Float64}, ref_value::Float64,
                                  ref_indices::Vector{Int}, ref_min_alt::Float64, ref_max_alt::Float64,
                                  output_path::String="")
    
    # Create comparison plot
    p = plot(layout=(2,1), size=(800, 600))
    
    # Plot range-corrected signal
    plot!(p[1], altitude/1000, range_corrected,
          title="Range-Corrected Signal (X²)",
          xlabel="Altitude (km)",
          ylabel="Signal × Range²",
          label="Range Corrected",
          linewidth=1,
          color=:green)
    
    # Highlight reference region
    vspan!(p[1], [ref_min_alt/1000, ref_max_alt/1000],
           alpha=0.2,
           color=:orange,
           label="Reference Region")
    
    # Add reference line
    hline!(p[1], [ref_value],
           label="Reference Value",
           linestyle=:dash,
           color=:red,
           linewidth=2)
    
    # Plot normalized signal
    plot!(p[2], altitude/1000, normalized,
          title="Normalized Signal (Xⁿ²)",
          xlabel="Altitude (km)",
          ylabel="Normalized Signal",
          label="Normalized",
          linewidth=1,
          color=:purple)
    
    # Add unity reference line
    hline!(p[2], [1.0],
           label="Unity Reference",
           linestyle=:dash,
           color=:black,
           linewidth=2)
    
    # Highlight reference region in normalized plot
    vspan!(p[2], [ref_min_alt/1000, ref_max_alt/1000],
           alpha=0.2,
           color=:orange,
           label="Reference Region")
    
    # Overall title
    plot!(p, plot_title="Signal Normalization Analysis")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ Normalization plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_normalization(input_dir::String="data", output_dir::String="data";
                               plot_results::Bool=true) -> Vector{String}

Batch process normalization for all range-corrected CSV files in a directory.
"""
function batch_process_normalization(input_dir::String="data", output_dir::String="data";
                                    plot_results::Bool=true)
    
    println("="^50)
    println("BATCH NORMALIZATION PROCESSING")
    println("="^50)
    
    # Find all range-corrected CSV files
    csv_files = filter(f -> endswith(f, "range_corrected.csv") && !contains(f, "normalized"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No range-corrected CSV files found in $input_dir")
        println("    Run range correction first.")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            base_name = replace(splitext(basename(csv_file))[1], "_range_corrected" => "")
            output_csv = joinpath(output_dir, "$(base_name)_normalized.csv")
            
            processed_file = process_normalization_csv(csv_file, output_csv, 
                                                     plot_results=plot_results, 
                                                     save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("NORMALIZATION BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export select_reference_region, normalize_signal
export process_normalization_csv, create_normalization_plot
export batch_process_normalization
