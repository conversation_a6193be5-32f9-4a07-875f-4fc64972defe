# RTDI (Range-Time-Display-Intensity) Map Generator Module
# Create RTDI maps from multiple LIDAR files at different processing stages
# Supports CSV-based data pipeline with batch processing

using CSV
using DataFrames
using Dates
using Plots
using Statistics

"""
    create_rtdi_map_from_csv(csv_files::Vector{String};
                            data_column::String="Photon_Counts",
                            max_altitude::Float64=15000.0,
                            time_resolution_minutes::Float64=5.0) -> <PERSON>ple{Matrix{Float64}, Vector{Float64}, Vector{DateTime}}

Create RTDI map from multiple CSV files.
Data columns: "Photon_Counts", "Noise_Corrected", "Range_Corrected", "Normalized", etc.
"""
function create_rtdi_map_from_csv(csv_files::Vector{String};
                                 data_column::String="Photon_Counts",
                                 max_altitude::Float64=15000.0,
                                 time_resolution_minutes::Float64=5.0)

    if isempty(csv_files)
        error("No CSV files provided")
    end

    println("Creating RTDI map from $(length(csv_files)) CSV files...")
    println("Data column: $data_column")
    println("Max altitude: $(max_altitude/1000) km")

    # Read first file to get altitude grid
    first_df = CSV.read(csv_files[1], DataFrame, comment="#")
    
    if !("Altitude_m" in names(first_df))
        error("Altitude_m column not found in CSV files")
    end
    
    if !(data_column in names(first_df))
        error("Data column '$data_column' not found in CSV files")
    end

    # Create altitude grid (filter by max altitude)
    altitude_indices = findall(alt -> alt <= max_altitude, first_df.Altitude_m)
    altitude_grid = first_df.Altitude_m[altitude_indices]

    # Initialize intensity matrix
    intensity_matrix = Matrix{Float64}(undef, length(altitude_indices), 0)
    time_stamps = DateTime[]

    # Process each file
    for (i, file) in enumerate(csv_files)
        try
            # Read CSV data
            df = CSV.read(file, DataFrame, comment="#")
            
            # Extract signal data
            signal_data = df[altitude_indices, data_column]
            
            # Extract timestamp from metadata or filename
            timestamp = extract_timestamp_from_csv(file)
            
            # Add to matrix
            intensity_matrix = hcat(intensity_matrix, signal_data)
            push!(time_stamps, timestamp)

            if i % 10 == 0
                println("Processed $i/$(length(csv_files)) files")
            end

        catch e
            println("Error processing file $(basename(file)): $e")
        end
    end

    println("RTDI map created: $(size(intensity_matrix, 1)) altitudes × $(size(intensity_matrix, 2)) time points")

    return intensity_matrix, altitude_grid, time_stamps
end

"""
    extract_timestamp_from_csv(csv_file::String) -> DateTime

Extract timestamp from CSV metadata or filename.
"""
function extract_timestamp_from_csv(csv_file::String)
    # Try to extract from metadata first
    try
        open(csv_file, "r") do file
            for line in eachline(file)
                if startswith(line, "# Start Time:")
                    time_str = strip(split(line, ":")[2:end] |> x -> join(x, ":"))
                    return DateTime(time_str)
                end
            end
        end
    catch e
        # Fall back to filename parsing
    end
    
    # Extract from filename pattern (e.g., "20230508_1234.csv")
    filename = basename(csv_file)
    
    # Try various filename patterns
    patterns = [
        r"(\d{8})_(\d{4})",  # YYYYMMDD_HHMM
        r"(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2})",  # YYYY-MM-DD_HH-MM
        r"(\d{2}[A-Za-z]{3}\d{4})"  # DDMmmYYYY
    ]
    
    for pattern in patterns
        m = match(pattern, filename)
        if m !== nothing
            try
                if length(m.captures) == 2
                    date_part, time_part = m.captures
                    if length(date_part) == 8  # YYYYMMDD
                        year = parse(Int, date_part[1:4])
                        month = parse(Int, date_part[5:6])
                        day = parse(Int, date_part[7:8])
                        hour = parse(Int, time_part[1:2])
                        minute = parse(Int, time_part[3:4])
                        return DateTime(year, month, day, hour, minute)
                    end
                else
                    # Handle single capture (date only)
                    date_str = m.captures[1]
                    if occursin(r"\d{2}[A-Za-z]{3}\d{4}", date_str)
                        # Parse DDMmmYYYY format
                        day = parse(Int, date_str[1:2])
                        month_str = date_str[3:5]
                        year = parse(Int, date_str[6:9])
                        
                        month_map = Dict("Jan"=>1, "Feb"=>2, "Mar"=>3, "Apr"=>4, "May"=>5, "Jun"=>6,
                                       "Jul"=>7, "Aug"=>8, "Sep"=>9, "Oct"=>10, "Nov"=>11, "Dec"=>12)
                        month = month_map[month_str]
                        
                        return DateTime(year, month, day)
                    end
                end
            catch e
                continue
            end
        end
    end
    
    # Default fallback
    return DateTime(2023, 5, 8, 12, 0, 0)
end

"""
    plot_rtdi_map(intensity_matrix::Matrix{Float64},
                  altitude_grid::Vector{Float64},
                  time_stamps::Vector{DateTime};
                  title_suffix::String="",
                  colormap=:viridis,
                  log_scale::Bool=true,
                  save_plot::Bool=true,
                  output_path::String="plots/rtdi_map.png") -> Plots.Plot

Plot RTDI map with proper scaling and formatting.
"""
function plot_rtdi_map(intensity_matrix::Matrix{Float64},
                      altitude_grid::Vector{Float64},
                      time_stamps::Vector{DateTime};
                      title_suffix::String="",
                      colormap=:viridis,
                      log_scale::Bool=true,
                      save_plot::Bool=true,
                      output_path::String="plots/rtdi_map.png")

    # Prepare data for plotting
    plot_data = copy(intensity_matrix)

    if log_scale
        # Apply log scaling (add small value to avoid log(0))
        plot_data = log10.(plot_data .+ 1e-10)
        colorbar_label = "log₁₀(Intensity)"
    else
        colorbar_label = "Intensity"
    end

    # Create time axis (convert to hours from start)
    if !isempty(time_stamps)
        start_time = minimum(time_stamps)
        time_hours = [(t - start_time).value / (1000 * 3600) for t in time_stamps]  # Convert to hours
    else
        time_hours = 1:size(plot_data, 2)
    end

    # Create the heatmap
    p = heatmap(time_hours, altitude_grid/1000, plot_data,
               title="RTDI Map$title_suffix",
               xlabel="Time (hours from start)",
               ylabel="Altitude (km)",
               color=colormap,
               aspect_ratio=:auto,
               size=(1000, 600))

    # Add colorbar
    plot!(colorbar_title=colorbar_label)

    if save_plot
        mkpath(dirname(output_path))
        savefig(p, output_path)
        println("📊 RTDI map saved: $output_path")
    end

    return p
end

"""
    generate_rtdi_maps_for_day(data_directory::String;
                              processing_stages::Vector{String}=["Photon_Counts", "Noise_Corrected", "Range_Corrected", "Normalized"],
                              max_altitude::Float64=15000.0,
                              output_dir::String="plots") -> Dict{String, Any}

Generate RTDI maps for all processing stages for a single day of data.
"""
function generate_rtdi_maps_for_day(data_directory::String;
                                   processing_stages::Vector{String}=["Photon_Counts", "Noise_Corrected", "Range_Corrected", "Normalized"],
                                   max_altitude::Float64=15000.0,
                                   output_dir::String="plots")

    if !isdir(data_directory)
        error("Directory not found: $data_directory")
    end

    # Find all CSV files in directory
    csv_files = String[]
    for file in readdir(data_directory, join=true)
        if endswith(file, ".csv")
            push!(csv_files, file)
        end
    end

    if isempty(csv_files)
        error("No CSV files found in $data_directory")
    end

    println("Generating RTDI maps for $(length(csv_files)) files...")
    println("Processing stages: $processing_stages")

    rtdi_results = Dict{String, Any}()

    for stage in processing_stages
        println("\nProcessing stage: $stage")

        try
            # Create RTDI map
            intensity_matrix, altitude_grid, time_stamps = create_rtdi_map_from_csv(csv_files,
                                                                                   data_column=stage,
                                                                                   max_altitude=max_altitude)

            # Plot RTDI map
            day_name = basename(data_directory)
            output_path = joinpath(output_dir, "rtdi_$(day_name)_$(stage).png")
            plot_rtdi_map(intensity_matrix, altitude_grid, time_stamps,
                         title_suffix=" - $day_name ($stage)",
                         save_plot=true,
                         output_path=output_path)

            # Store results
            rtdi_results[stage] = Dict(
                "intensity_matrix" => intensity_matrix,
                "altitude_grid" => altitude_grid,
                "time_stamps" => time_stamps,
                "plot_path" => output_path
            )

        catch e
            println("Error creating RTDI map for stage $stage: $e")
        end
    end

    return rtdi_results
end

"""
    batch_generate_rtdi_maps(input_dirs::Vector{String};
                            processing_stages::Vector{String}=["Photon_Counts", "Noise_Corrected", "Range_Corrected", "Normalized"],
                            max_altitude::Float64=15000.0,
                            output_dir::String="plots") -> Dict{String, Any}

Batch generate RTDI maps for multiple days of data.
"""
function batch_generate_rtdi_maps(input_dirs::Vector{String};
                                 processing_stages::Vector{String}=["Photon_Counts", "Noise_Corrected", "Range_Corrected", "Normalized"],
                                 max_altitude::Float64=15000.0,
                                 output_dir::String="plots")

    all_results = Dict{String, Any}()

    for data_dir in input_dirs
        if isdir(data_dir)
            day_name = basename(data_dir)
            println("\n" * "="^60)
            println("Processing day: $day_name")
            println("="^60)

            try
                day_results = generate_rtdi_maps_for_day(data_dir,
                                                        processing_stages=processing_stages,
                                                        max_altitude=max_altitude,
                                                        output_dir=output_dir)
                all_results[day_name] = day_results

            catch e
                println("❌ Error processing day $day_name: $e")
            end
        else
            println("⚠️  Directory not found: $data_dir")
        end
    end

    println("\n✅ RTDI batch processing complete!")
    println("📁 Results saved in: $output_dir")

    return all_results
end

"""
    save_rtdi_data_csv(intensity_matrix::Matrix{Float64},
                      altitude_grid::Vector{Float64},
                      time_stamps::Vector{DateTime},
                      output_path::String)

Save RTDI map data as CSV for further analysis.
"""
function save_rtdi_data_csv(intensity_matrix::Matrix{Float64},
                           altitude_grid::Vector{Float64},
                           time_stamps::Vector{DateTime},
                           output_path::String)

    # Create DataFrame with altitude as rows and time as columns
    df = DataFrame()
    df.Altitude_m = altitude_grid

    # Add time columns
    for (i, timestamp) in enumerate(time_stamps)
        col_name = "T_$(Dates.format(timestamp, "HH_MM"))"
        df[!, col_name] = intensity_matrix[:, i]
    end

    # Save with metadata
    mkpath(dirname(output_path))

    open(output_path, "w") do file
        write(file, "# RTDI Map Data\n")
        write(file, "# Generated: $(Dates.now())\n")
        write(file, "# Altitude points: $(length(altitude_grid))\n")
        write(file, "# Time points: $(length(time_stamps))\n")
        write(file, "# Time range: $(minimum(time_stamps)) to $(maximum(time_stamps))\n")
        write(file, "#\n")
    end

    CSV.write(output_path, df, append=true)
    println("📁 RTDI data saved: $output_path")
end

"""
    create_rtdi_map_from_raw_files(input_folder::String, max_range::Int=1000, max_files::Int=2701) -> Matrix{UInt32}

Create RTDI map directly from raw LICEL files using RTI_Plotting approach.
Matches the exact processing from RTI_Plotting.jl with UInt32 data handling.
"""
function create_rtdi_map_from_raw_files(input_folder::String, max_range::Int=1000, max_files::Int=2701)
    if !isdir(input_folder)
        error("Input folder not found: $input_folder")
    end

    println("Creating RTDI map from raw files in: $input_folder")
    println("Max range: $max_range bins, Max files: $max_files")

    # Initialize RTDI matrix with UInt32 (matching RTI_Plotting)
    combined_rtdi_data = zeros(UInt32, max_range, max_files)

    # Get all files in the folder
    files = readdir(input_folder)
    file_counter = 0

    for file in files
        if file_counter >= max_files
            break
        end

        input_file_path = joinpath(input_folder, file)

        if isfile(input_file_path)
            try
                open(input_file_path, "r") do input_file
                    # Read header lines (3 lines to match pre-processing)
                    header_lines = [readline(input_file) for _ in 1:3]

                    # Read raw UInt32 data
                    raw_data_values = UInt32[]
                    while !eof(input_file)
                        raw_data_value = read(input_file, UInt32)
                        push!(raw_data_values, raw_data_value)
                    end

                    # Noise correction factor (matching RTI_Plotting approach)
                    # Use range 1000-2000 for noise estimation
                    noise_start = min(max_range, 1000)
                    noise_end = min(length(raw_data_values), 2000)

                    if noise_end > noise_start
                        noise_correcting_factor = mean(raw_data_values[noise_start:noise_end])
                        noise_correcting_factor = round(Int, noise_correcting_factor)
                    else
                        noise_correcting_factor = 0
                    end

                    # Apply noise and range corrections (matching RTI_Plotting)
                    for i in 5:min(max_range, length(raw_data_values))
                        # Noise correction
                        corrected_value = max(raw_data_values[i] - noise_correcting_factor, 0)

                        # Range correction: range = 15 + (i-1) * 30
                        range = 15 + (i - 1) * 30
                        range_squared = UInt64(range) * UInt64(range)

                        # Apply range-squared correction
                        final_value = UInt64(corrected_value) * range_squared

                        # Handle overflow (cap at UInt32 max)
                        if final_value > UInt32(0xffffffff)
                            final_value = UInt32(0xffffffff)
                        end

                        combined_rtdi_data[i, file_counter+1] = UInt32(final_value)
                    end

                    println("Processed file: $file")
                    file_counter += 1
                end
            catch e
                println("Error processing file $file: $e")
            end
        end
    end

    println("RTDI map created: $(size(combined_rtdi_data, 1)) range bins × $(size(combined_rtdi_data, 2)) time points")
    println("Processed $file_counter files")

    return combined_rtdi_data
end

"""
    save_rtdi_heatmap(rtdi_data::Matrix{UInt32}, output_path::String;
                     title::String="RTDI Heatmap", colormap=:thermal)

Save RTDI heatmap plot matching RTI_Plotting visualization.
"""
function save_rtdi_heatmap(rtdi_data::Matrix{UInt32}, output_path::String;
                          title::String="RTDI Heatmap", colormap=:thermal)

    max_range, max_files = size(rtdi_data)

    # Create heatmap (matching RTI_Plotting style)
    plot_heatmap = heatmap(1:max_files, 1:max_range, rtdi_data,
                          c=colormap,
                          xlabel="File Index",
                          ylabel="Range Bin",
                          title=title,
                          aspect_ratio=:auto,
                          size=(1000, 600))

    # Save plot
    mkpath(dirname(output_path))
    savefig(plot_heatmap, output_path)
    println("RTDI heatmap saved: $output_path")

    return plot_heatmap
end

"""
    batch_create_rtdi_maps(input_folder::String, output_folder::String;
                          initial_max_range::Int=500, final_max_range::Int=1000, step::Int=50)

Batch create RTDI maps for different range settings (matching RTI_Plotting workflow).
"""
function batch_create_rtdi_maps(input_folder::String, output_folder::String;
                               initial_max_range::Int=500, final_max_range::Int=1000, step::Int=50)

    if !isdir(output_folder)
        mkpath(output_folder)
    end

    println("Batch creating RTDI maps...")
    println("Input folder: $input_folder")
    println("Output folder: $output_folder")
    println("Range: $initial_max_range to $final_max_range (step: $step)")

    for current_max_range in initial_max_range:step:final_max_range
        println("\nProcessing max_range: $current_max_range")

        # Create RTDI map
        rtdi_data = create_rtdi_map_from_raw_files(input_folder, current_max_range)

        # Generate filename with serial number (matching RTI_Plotting)
        figure_number = div((current_max_range - initial_max_range), step) + 50
        filename = joinpath(output_folder, "$(figure_number).png")

        # Save heatmap
        save_rtdi_heatmap(rtdi_data, filename,
                         title="RTDI Map - Range: $current_max_range bins")
    end

    println("\nBatch RTDI generation complete!")
end

# Export main functions
export create_rtdi_map_from_csv, plot_rtdi_map
export generate_rtdi_maps_for_day, batch_generate_rtdi_maps
export save_rtdi_data_csv, extract_timestamp_from_csv
export create_rtdi_map_from_raw_files, save_rtdi_heatmap, batch_create_rtdi_maps
