# Backscatter Analysis Module
# Compute total and aerosol backscatter coefficients
# Augments CSV data with backscatter analysis columns

using CSV
using DataFrames
using Statistics
using Plots

"""
    calculate_total_backscatter(normalized_signal::Vector{Float64}, 
                               beta_molecular::Vector{Float64},
                               ref_indices::Vector{Bool}) -> Vector{Float64}

Calculate total backscatter coefficient βₜₒₜₐₗ(z) proportional to Xⁿ²(z).
Uses molecular backscatter in reference region for calibration.
"""
function calculate_total_backscatter(normalized_signal::Vector{Float64}, 
                                    beta_molecular::Vector{Float64},
                                    ref_indices::Vector{Bool})
    
    # Find reference region indices
    ref_idx = findall(ref_indices)
    
    if isempty(ref_idx)
        error("No reference indices found for calibration")
    end
    
    # Calculate calibration constant using reference region
    # Assume clean atmosphere in reference region: βₜₒₜₐₗ ≈ βₘ
    ref_normalized = mean(normalized_signal[ref_idx])
    ref_molecular = mean(beta_molecular[ref_idx])
    
    # Calibration constant
    calibration_constant = ref_molecular / ref_normalized
    
    # Calculate total backscatter
    beta_total = normalized_signal .* calibration_constant
    
    println("Total backscatter calibration:")
    println("  Reference normalized signal: $(ref_normalized:.3f)")
    println("  Reference molecular backscatter: $(ref_molecular:.2e) m⁻¹ sr⁻¹")
    println("  Calibration constant: $(calibration_constant:.2e)")
    
    return beta_total
end

"""
    calculate_aerosol_backscatter(beta_total::Vector{Float64}, 
                                 beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate aerosol backscatter coefficient: βₐ(z) = βₜₒₜₐₗ(z) - βₘ(z)
"""
function calculate_aerosol_backscatter(beta_total::Vector{Float64}, 
                                      beta_molecular::Vector{Float64})
    
    if length(beta_total) != length(beta_molecular)
        error("Total and molecular backscatter vectors must have the same length")
    end
    
    # Calculate aerosol backscatter
    beta_aerosol = beta_total .- beta_molecular
    
    # Set negative values to zero (no negative aerosol backscatter)
    beta_aerosol = max.(beta_aerosol, 0.0)
    
    return beta_aerosol
end

"""
    process_backscatter_analysis_csv(input_csv::String, output_csv::String="";
                                    plot_results::Bool=true, save_plots::Bool=true) -> String

Process backscatter analysis on CSV data and augment with new columns.
"""
function process_backscatter_analysis_csv(input_csv::String, output_csv::String="";
                                         plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_backscatter.csv")
    end
    
    println("Processing backscatter analysis for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["X2_normalized", "Beta_molecular", "Reference_Indices", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV. Run normalization and molecular backscatter first.")
        end
    end
    
    # Extract data
    normalized_signal = df.X2_normalized
    beta_molecular = df.Beta_molecular
    ref_indices = df.Reference_Indices
    altitude = df.Altitude_m
    
    # Calculate total backscatter
    beta_total = calculate_total_backscatter(normalized_signal, beta_molecular, ref_indices)
    
    # Calculate aerosol backscatter
    beta_aerosol = calculate_aerosol_backscatter(beta_total, beta_molecular)
    
    println("Backscatter analysis results:")
    println("  Total backscatter range: $(minimum(beta_total):.2e) - $(maximum(beta_total):.2e) m⁻¹ sr⁻¹")
    println("  Aerosol backscatter range: $(minimum(beta_aerosol):.2e) - $(maximum(beta_aerosol):.2e) m⁻¹ sr⁻¹")
    println("  Max aerosol/molecular ratio: $(maximum(beta_aerosol ./ beta_molecular):.2f)")
    
    # Add new columns to DataFrame
    df.Beta_total = beta_total
    df.Beta_aerosol = beta_aerosol
    df.Beta_total_1e6 = beta_total .* 1e6      # Convert to 10⁻⁶ m⁻¹ sr⁻¹ units
    df.Beta_aerosol_1e6 = beta_aerosol .* 1e6  # Convert to 10⁻⁶ m⁻¹ sr⁻¹ units
    df.Aerosol_Molecular_Ratio = beta_aerosol ./ beta_molecular
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add backscatter analysis metadata
    push!(metadata_lines, "# Backscatter Analysis Applied")
    push!(metadata_lines, "# Total Backscatter Range: $(minimum(beta_total):.2e) - $(maximum(beta_total):.2e) m⁻¹ sr⁻¹")
    push!(metadata_lines, "# Aerosol Backscatter Range: $(minimum(beta_aerosol):.2e) - $(maximum(beta_aerosol):.2e) m⁻¹ sr⁻¹")
    push!(metadata_lines, "# Max Aerosol/Molecular Ratio: $(maximum(beta_aerosol ./ beta_molecular):.2f)")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ Backscatter analysis CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_backscatter_analysis.png")
            mkpath("plots")
        end
        
        create_backscatter_analysis_plot(altitude, beta_molecular, beta_total, beta_aerosol, 
                                        ref_indices, plot_filename)
    end
    
    return output_csv
end

"""
    create_backscatter_analysis_plot(altitude::Vector{Float64}, beta_molecular::Vector{Float64},
                                    beta_total::Vector{Float64}, beta_aerosol::Vector{Float64},
                                    ref_indices::Vector{Bool}, output_path::String="")

Create backscatter coefficients comparison plot.
"""
function create_backscatter_analysis_plot(altitude::Vector{Float64}, beta_molecular::Vector{Float64},
                                         beta_total::Vector{Float64}, beta_aerosol::Vector{Float64},
                                         ref_indices::Vector{Bool}, output_path::String="")
    
    # Create backscatter profiles plot
    p = plot(layout=(1,3), size=(1200, 400))
    
    # Molecular backscatter
    plot!(p[1], beta_molecular.*1e6, altitude/1000,
          title="Molecular Backscatter",
          xlabel="βₘ (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:blue,
          label="Molecular")
    
    # Total backscatter
    plot!(p[2], beta_total.*1e6, altitude/1000,
          title="Total Backscatter",
          xlabel="βₜₒₜₐₗ (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:red,
          label="Total")
    
    # Add molecular reference
    plot!(p[2], beta_molecular.*1e6, altitude/1000,
          linewidth=1,
          color=:blue,
          linestyle=:dash,
          label="Molecular")
    
    # Aerosol backscatter
    plot!(p[3], beta_aerosol.*1e6, altitude/1000,
          title="Aerosol Backscatter",
          xlabel="βₐ (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:green,
          label="Aerosol")
    
    # Highlight reference region in all plots
    ref_idx = findall(ref_indices)
    if !isempty(ref_idx)
        ref_alt_min = minimum(altitude[ref_idx]) / 1000
        ref_alt_max = maximum(altitude[ref_idx]) / 1000
        
        for i in 1:3
            hspan!(p[i], [ref_alt_min, ref_alt_max],
                   alpha=0.2,
                   color=:orange,
                   label=i==1 ? "Reference" : "")
        end
    end
    
    # Overall title
    plot!(p, plot_title="Backscatter Coefficients Analysis")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ Backscatter analysis plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_backscatter_analysis(input_dir::String="data", output_dir::String="data";
                                      plot_results::Bool=true) -> Vector{String}

Batch process backscatter analysis for all molecular CSV files in a directory.
"""
function batch_process_backscatter_analysis(input_dir::String="data", output_dir::String="data";
                                           plot_results::Bool=true)
    
    println("="^50)
    println("BATCH BACKSCATTER ANALYSIS PROCESSING")
    println("="^50)
    
    # Find all molecular CSV files that have normalization data
    csv_files = filter(f -> endswith(f, "molecular.csv") && !contains(f, "backscatter"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No molecular CSV files found in $input_dir")
        println("    Run molecular backscatter calculation first.")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            # Extract base name (remove all processing suffixes)
            base_name = replace(splitext(basename(csv_file))[1], "_molecular" => "")
            output_csv = joinpath(output_dir, "$(base_name)_backscatter.csv")
            
            processed_file = process_backscatter_analysis_csv(csv_file, output_csv, 
                                                            plot_results=plot_results, 
                                                            save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("BACKSCATTER ANALYSIS BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export calculate_total_backscatter, calculate_aerosol_backscatter
export process_backscatter_analysis_csv, create_backscatter_analysis_plot
export batch_process_backscatter_analysis
