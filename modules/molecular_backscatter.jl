# Molecular Backscatter Module
# Calculate βₘ(z) using standard atmosphere models
# Augments CSV data with molecular backscatter columns

using CSV
using DataFrames
using Plots

# Physical constants (avoid redefinition)
if !@isdefined(AVOGADRO)
    global AVOGADRO = 6.02214076e23      # mol⁻¹
end
if !@isdefined(BOLTZMANN)
    global BOLTZMANN = 1.380649e-23      # J K⁻¹
end
if !@isdefined(MOLECULAR_WEIGHT_AIR)
    global MOLECULAR_WEIGHT_AIR = 28.964e-3  # kg/mol
end
if !@isdefined(LASER_WAVELENGTH)
    global LASER_WAVELENGTH = 1064e-9    # m (typical Nd:YAG laser)
end
if !@isdefined(KING_FACTOR)
    global KING_FACTOR = 1.0455          # King correction factor for air
end

"""
    standard_atmosphere_pressure(altitude::Float64) -> Float64

Calculate atmospheric pressure using standard atmosphere model.
Pressure in Pa, altitude in meters.
"""
function standard_atmosphere_pressure(altitude::Float64)
    # Standard atmosphere model (simplified)
    P0 = 101325.0  # Sea level pressure (Pa)
    T0 = 288.15    # Sea level temperature (K)
    L = 0.0065     # Temperature lapse rate (K/m)
    g = 9.80665    # Gravitational acceleration (m/s²)
    R = 287.0      # Specific gas constant for air (J/kg/K)
    
    if altitude <= 11000.0  # Troposphere
        T = T0 - L * altitude
        P = P0 * (T / T0)^(g / (R * L))
    else  # Simplified stratosphere
        T11 = T0 - L * 11000.0
        P11 = P0 * (T11 / T0)^(g / (R * L))
        P = P11 * exp(-g * (altitude - 11000.0) / (R * T11))
    end
    
    return P
end

"""
    standard_atmosphere_temperature(altitude::Float64) -> Float64

Calculate atmospheric temperature using standard atmosphere model.
Temperature in K, altitude in meters.
"""
function standard_atmosphere_temperature(altitude::Float64)
    T0 = 288.15    # Sea level temperature (K)
    L = 0.0065     # Temperature lapse rate (K/m)
    
    if altitude <= 11000.0  # Troposphere
        T = T0 - L * altitude
    else  # Simplified stratosphere (isothermal)
        T = T0 - L * 11000.0
    end
    
    return T
end

"""
    calculate_molecular_backscatter(altitude::Vector{Float64};
                                   wavelength::Float64=LASER_WAVELENGTH) -> Vector{Float64}

Calculate molecular backscatter coefficient βₘ(z) using atmospheric profiles.
Returns backscatter coefficient in m⁻¹ sr⁻¹.
"""
function calculate_molecular_backscatter(altitude::Vector{Float64};
                                        wavelength::Float64=LASER_WAVELENGTH)
    
    n_points = length(altitude)
    beta_molecular = zeros(n_points)
    
    for i in 1:n_points
        alt = altitude[i]
        
        # Get atmospheric parameters
        P = standard_atmosphere_pressure(alt)  # Pa
        T = standard_atmosphere_temperature(alt)  # K
        
        # Calculate number density
        n_density = P / (BOLTZMANN * T)  # molecules/m³
        
        # Rayleigh scattering cross-section
        # σ = (24π³/λ⁴) * (n²-1)²/(n²+2)² * F_K
        # For air at 1064 nm: n ≈ 1.000293
        n_refract = 1.000293  # Refractive index of air at 1064 nm
        
        sigma_rayleigh = (24 * π^3 / wavelength^4) * 
                        ((n_refract^2 - 1)^2 / (n_refract^2 + 2)^2) * 
                        KING_FACTOR
        
        # Molecular backscatter coefficient
        beta_molecular[i] = n_density * sigma_rayleigh / (4 * π)
    end
    
    return beta_molecular
end

"""
    process_molecular_backscatter_csv(input_csv::String, output_csv::String="";
                                     wavelength::Float64=LASER_WAVELENGTH,
                                     plot_results::Bool=true, save_plots::Bool=true) -> String

Process molecular backscatter calculation on CSV data and augment with new columns.
"""
function process_molecular_backscatter_csv(input_csv::String, output_csv::String="";
                                          wavelength::Float64=LASER_WAVELENGTH,
                                          plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_molecular.csv")
    end
    
    println("Processing molecular backscatter for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV.")
        end
    end
    
    # Extract data
    altitude = df.Altitude_m
    
    # Calculate molecular backscatter
    beta_molecular = calculate_molecular_backscatter(altitude, wavelength=wavelength)
    
    # Calculate atmospheric profiles for reference
    pressures = [standard_atmosphere_pressure(alt) for alt in altitude]
    temperatures = [standard_atmosphere_temperature(alt) for alt in altitude]
    
    println("Molecular backscatter calculation:")
    println("  Wavelength: $(wavelength*1e9:.1f) nm")
    println("  Altitude range: $(minimum(altitude):.1f) - $(maximum(altitude):.1f) m")
    println("  βₘ range: $(minimum(beta_molecular):.2e) - $(maximum(beta_molecular):.2e) m⁻¹ sr⁻¹")
    
    # Add new columns to DataFrame
    df.Beta_molecular = beta_molecular
    df.Pressure_Pa = pressures
    df.Temperature_K = temperatures
    df.Beta_molecular_1e6 = beta_molecular .* 1e6  # Convert to 10⁻⁶ m⁻¹ sr⁻¹ units
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add molecular backscatter metadata
    push!(metadata_lines, "# Molecular Backscatter Calculation Applied")
    push!(metadata_lines, "# Laser Wavelength: $(wavelength*1e9:.1f) nm")
    push!(metadata_lines, "# King Factor: $(KING_FACTOR)")
    push!(metadata_lines, "# Refractive Index (air, 1064nm): 1.000293")
    push!(metadata_lines, "# βₘ Range: $(minimum(beta_molecular):.2e) - $(maximum(beta_molecular):.2e) m⁻¹ sr⁻¹")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ Molecular backscatter CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_molecular_backscatter.png")
            mkpath("plots")
        end
        
        create_molecular_backscatter_plot(altitude, pressures, temperatures, beta_molecular, plot_filename)
    end
    
    return output_csv
end

"""
    create_molecular_backscatter_plot(altitude::Vector{Float64}, pressures::Vector{Float64},
                                     temperatures::Vector{Float64}, beta_molecular::Vector{Float64},
                                     output_path::String="")

Create atmospheric profiles plot for molecular backscatter analysis.
"""
function create_molecular_backscatter_plot(altitude::Vector{Float64}, pressures::Vector{Float64},
                                          temperatures::Vector{Float64}, beta_molecular::Vector{Float64},
                                          output_path::String="")
    
    # Create atmospheric profiles plot
    p = plot(layout=(1,3), size=(1200, 400))
    
    # Pressure profile
    plot!(p[1], pressures./100, altitude/1000,  # Convert Pa to hPa
          title="Atmospheric Pressure",
          xlabel="Pressure (hPa)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:blue)
    
    # Temperature profile
    plot!(p[2], temperatures, altitude/1000,
          title="Atmospheric Temperature",
          xlabel="Temperature (K)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:red)
    
    # Molecular backscatter profile
    plot!(p[3], beta_molecular.*1e6, altitude/1000,  # Convert to 10⁻⁶ m⁻¹ sr⁻¹
          title="Molecular Backscatter",
          xlabel="βₘ (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:green)
    
    # Overall title
    plot!(p, plot_title="Atmospheric Profiles and Molecular Backscatter")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ Molecular backscatter plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_molecular_backscatter(input_dir::String="data", output_dir::String="data";
                                       plot_results::Bool=true) -> Vector{String}

Batch process molecular backscatter for all CSV files in a directory.
"""
function batch_process_molecular_backscatter(input_dir::String="data", output_dir::String="data";
                                            plot_results::Bool=true)
    
    println("="^50)
    println("BATCH MOLECULAR BACKSCATTER PROCESSING")
    println("="^50)
    
    # Find all CSV files (can work with any stage)
    csv_files = filter(f -> endswith(f, ".csv") && !contains(f, "molecular"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No CSV files found in $input_dir")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            # Extract base name (remove all processing suffixes)
            base_name = splitext(basename(csv_file))[1]
            base_name = replace(base_name, r"_(noise_corrected|range_corrected|normalized)" => "")
            output_csv = joinpath(output_dir, "$(base_name)_molecular.csv")
            
            processed_file = process_molecular_backscatter_csv(csv_file, output_csv, 
                                                             plot_results=plot_results, 
                                                             save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("MOLECULAR BACKSCATTER BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export standard_atmosphere_pressure, standard_atmosphere_temperature
export calculate_molecular_backscatter
export process_molecular_backscatter_csv, create_molecular_backscatter_plot
export batch_process_molecular_backscatter
