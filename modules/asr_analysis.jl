# Aerosol Scattering Ratio (ASR) Analysis Module
# Calculate ASR(z) = βₜₒₜₐₗ(z) / βₘ(z) with interpretation guidelines
# Augments CSV data with ASR analysis columns

using CSV
using DataFrames
using Statistics
using Plots

"""
    calculate_aerosol_scattering_ratio(beta_total::Vector{Float64}, 
                                      beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate Aerosol Scattering Ratio: ASR(z) = βₜₒₜₐₗ(z) / βₘ(z)
"""
function calculate_aerosol_scattering_ratio(beta_total::Vector{Float64}, 
                                           beta_molecular::Vector{Float64})
    
    if length(beta_total) != length(beta_molecular)
        error("Total and molecular backscatter vectors must have the same length")
    end
    
    # Calculate ASR, avoiding division by zero
    asr = zeros(length(beta_total))
    for i in 1:length(beta_total)
        if beta_molecular[i] > 0
            asr[i] = beta_total[i] / beta_molecular[i]
        else
            asr[i] = 1.0  # Default to clean air value
        end
    end
    
    return asr
end

"""
    interpret_asr_values(asr::Vector{Float64}) -> Dict{String, Any}

Provide interpretation of ASR values with atmospheric condition categories.
"""
function interpret_asr_values(asr::Vector{Float64})
    interpretation = Dict{String, Any}()
    
    # Count points in different categories
    clean_air = count(x -> 0.9 <= x <= 1.1, asr)
    light_aerosol = count(x -> 1.1 < x <= 2.0, asr)
    moderate_aerosol = count(x -> 2.0 < x <= 5.0, asr)
    heavy_aerosol = count(x -> x > 5.0, asr)
    
    total_points = length(asr)
    
    interpretation["clean_air_fraction"] = clean_air / total_points
    interpretation["light_aerosol_fraction"] = light_aerosol / total_points
    interpretation["moderate_aerosol_fraction"] = moderate_aerosol / total_points
    interpretation["heavy_aerosol_fraction"] = heavy_aerosol / total_points
    
    interpretation["max_asr"] = maximum(asr)
    interpretation["mean_asr"] = mean(asr)
    interpretation["median_asr"] = median(asr)
    interpretation["std_asr"] = std(asr)
    
    # Atmospheric condition assessment
    if interpretation["clean_air_fraction"] > 0.8
        interpretation["overall_condition"] = "Clean"
    elseif interpretation["light_aerosol_fraction"] > 0.5
        interpretation["overall_condition"] = "Light Aerosol"
    elseif interpretation["moderate_aerosol_fraction"] > 0.3
        interpretation["overall_condition"] = "Moderate Aerosol"
    else
        interpretation["overall_condition"] = "Heavy Aerosol"
    end
    
    return interpretation
end

"""
    process_asr_analysis_csv(input_csv::String, output_csv::String="";
                            plot_results::Bool=true, save_plots::Bool=true) -> String

Process ASR analysis on CSV data and augment with new columns.
"""
function process_asr_analysis_csv(input_csv::String, output_csv::String="";
                                 plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_asr.csv")
    end
    
    println("Processing ASR analysis for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["Beta_total", "Beta_molecular", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV. Run backscatter analysis first.")
        end
    end
    
    # Extract data
    beta_total = df.Beta_total
    beta_molecular = df.Beta_molecular
    altitude = df.Altitude_m
    
    # Calculate ASR
    asr = calculate_aerosol_scattering_ratio(beta_total, beta_molecular)
    
    # Interpret results
    interpretation = interpret_asr_values(asr)
    
    println("Aerosol Scattering Ratio (ASR) Analysis:")
    println("  ASR range: $(minimum(asr):.2f) - $(maximum(asr):.2f)")
    println("  Mean ASR: $(interpretation["mean_asr"]:.2f)")
    println("  Median ASR: $(interpretation["median_asr"]:.2f)")
    println("  Overall condition: $(interpretation["overall_condition"])")
    println("  Clean air (ASR ≈ 1): $(interpretation["clean_air_fraction"]*100:.1f)%")
    println("  Light aerosol (1.1 < ASR ≤ 2): $(interpretation["light_aerosol_fraction"]*100:.1f)%")
    println("  Moderate aerosol (2 < ASR ≤ 5): $(interpretation["moderate_aerosol_fraction"]*100:.1f)%")
    println("  Heavy aerosol (ASR > 5): $(interpretation["heavy_aerosol_fraction"]*100:.1f)%")
    
    # Add new columns to DataFrame
    df.ASR = asr
    df.ASR_Category = map(asr) do x
        if 0.9 <= x <= 1.1
            "Clean"
        elseif 1.1 < x <= 2.0
            "Light_Aerosol"
        elseif 2.0 < x <= 5.0
            "Moderate_Aerosol"
        else
            "Heavy_Aerosol"
        end
    end
    df.ASR_Mean = fill(interpretation["mean_asr"], nrow(df))
    df.ASR_Std = fill(interpretation["std_asr"], nrow(df))
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add ASR analysis metadata
    push!(metadata_lines, "# Aerosol Scattering Ratio (ASR) Analysis Applied")
    push!(metadata_lines, "# ASR Range: $(minimum(asr):.2f) - $(maximum(asr):.2f)")
    push!(metadata_lines, "# Mean ASR: $(interpretation["mean_asr"]:.2f)")
    push!(metadata_lines, "# Overall Condition: $(interpretation["overall_condition"])")
    push!(metadata_lines, "# Clean Air Fraction: $(interpretation["clean_air_fraction"]*100:.1f)%")
    push!(metadata_lines, "# Light Aerosol Fraction: $(interpretation["light_aerosol_fraction"]*100:.1f)%")
    push!(metadata_lines, "# Moderate Aerosol Fraction: $(interpretation["moderate_aerosol_fraction"]*100:.1f)%")
    push!(metadata_lines, "# Heavy Aerosol Fraction: $(interpretation["heavy_aerosol_fraction"]*100:.1f)%")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ ASR analysis CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_asr_analysis.png")
            mkpath("plots")
        end
        
        create_asr_analysis_plot(altitude, asr, beta_total, beta_molecular, interpretation, plot_filename)
    end
    
    return output_csv
end

"""
    create_asr_analysis_plot(altitude::Vector{Float64}, asr::Vector{Float64},
                            beta_total::Vector{Float64}, beta_molecular::Vector{Float64},
                            interpretation::Dict{String, Any}, output_path::String="")

Create comprehensive ASR analysis plot with interpretation guidelines.
"""
function create_asr_analysis_plot(altitude::Vector{Float64}, asr::Vector{Float64},
                                 beta_total::Vector{Float64}, beta_molecular::Vector{Float64},
                                 interpretation::Dict{String, Any}, output_path::String="")
    
    # Create ASR analysis plot
    p = plot(layout=(1,2), size=(1000, 400))
    
    # ASR profile
    plot!(p[1], asr, altitude/1000,
          title="Aerosol Scattering Ratio",
          xlabel="ASR = βₜₒₜₐₗ / βₘ",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:purple,
          label="ASR")
    
    # Add interpretation lines
    vline!(p[1], [1.0],
           label="Clean Air (ASR = 1)",
           linestyle=:dash,
           color=:blue,
           linewidth=2)
    
    vline!(p[1], [2.0],
           label="Light Aerosol",
           linestyle=:dash,
           color=:orange,
           linewidth=1)
    
    vline!(p[1], [5.0],
           label="Heavy Aerosol",
           linestyle=:dash,
           color=:red,
           linewidth=1)
    
    # Set x-axis limits for better visualization
    xlims!(p[1], (0.5, min(10.0, maximum(asr) * 1.1)))
    
    # Backscatter comparison
    plot!(p[2], beta_total.*1e6, altitude/1000,
          title="Backscatter Coefficients",
          xlabel="Backscatter (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:red,
          label="Total")
    
    plot!(p[2], beta_molecular.*1e6, altitude/1000,
          linewidth=2,
          color=:blue,
          label="Molecular")
    
    # Add text annotation with interpretation
    annotate!(p[1], 0.7 * maximum(asr), 0.8 * maximum(altitude/1000),
             text("Condition: $(interpretation["overall_condition"])\nMean ASR: $(interpretation["mean_asr"]:.2f)", 8, :left))
    
    # Overall title
    plot!(p, plot_title="ASR Analysis - Atmospheric Condition Assessment")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ ASR analysis plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_asr_analysis(input_dir::String="data", output_dir::String="data";
                              plot_results::Bool=true) -> Vector{String}

Batch process ASR analysis for all backscatter CSV files in a directory.
"""
function batch_process_asr_analysis(input_dir::String="data", output_dir::String="data";
                                   plot_results::Bool=true)
    
    println("="^50)
    println("BATCH ASR ANALYSIS PROCESSING")
    println("="^50)
    
    # Find all backscatter CSV files
    csv_files = filter(f -> endswith(f, "backscatter.csv") && !contains(f, "asr"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No backscatter CSV files found in $input_dir")
        println("    Run backscatter analysis first.")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            # Extract base name (remove all processing suffixes)
            base_name = replace(splitext(basename(csv_file))[1], "_backscatter" => "")
            output_csv = joinpath(output_dir, "$(base_name)_asr.csv")
            
            processed_file = process_asr_analysis_csv(csv_file, output_csv, 
                                                    plot_results=plot_results, 
                                                    save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("ASR ANALYSIS BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export calculate_aerosol_scattering_ratio, interpret_asr_values
export process_asr_analysis_csv, create_asr_analysis_plot
export batch_process_asr_analysis
