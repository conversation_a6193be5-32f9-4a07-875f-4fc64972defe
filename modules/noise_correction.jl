# Noise Correction Module
# Implements noise estimation (36-60km altitude) and removal
# Augments CSV data with noise-corrected signal columns

using CSV
using DataFrames
using Statistics
using Plots

# Configuration constants (avoid redefinition)
if !@isdefined(NOISE_ALTITUDE_MIN)
    global NOISE_ALTITUDE_MIN = 36000.0  # 36 km
end
if !@isdefined(NOISE_ALTITUDE_MAX)
    global NOISE_ALTITUDE_MAX = 60000.0  # 60 km
end

"""
    estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64};
                        min_altitude::Float64=NOISE_ALTITUDE_MIN,
                        max_altitude::Float64=NOISE_ALTITUDE_MAX) -> Tuple{Float64, Vector{Int}}

Estimate noise level using high-altitude data where aerosols are absent.
Returns noise level and indices used for estimation.
"""
function estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64};
                             min_altitude::Float64=NOISE_ALTITUDE_MIN,
                             max_altitude::Float64=NOISE_ALTITUDE_MAX)
    
    # Find indices corresponding to noise estimation altitude range
    noise_indices = findall(alt -> min_altitude <= alt <= max_altitude, altitude)
    
    if isempty(noise_indices)
        @warn "No data points found in noise estimation range ($min_altitude - $max_altitude m)"
        # Use last 10% of data as fallback
        n_points = length(photon_counts)
        noise_indices = max(1, round(Int, 0.9 * n_points)):n_points
    end
    
    # Calculate noise as mean of signal in this range
    noise_signal = photon_counts[noise_indices]
    noise_level = mean(noise_signal)
    
    println("Noise estimation:")
    println("  Altitude range: $(minimum(altitude[noise_indices]):.1f) - $(maximum(altitude[noise_indices]):.1f) m")
    println("  Number of points: $(length(noise_indices))")
    println("  Noise level: $(noise_level:.2f)")
    println("  Noise std: $(std(noise_signal):.2f)")
    
    return noise_level, noise_indices
end

"""
    apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64) -> Vector{Float64}

Apply noise correction: S_clean(z) = S_raw(z) - Noise
Ensures no negative values by setting minimum to small positive value.
"""
function apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64)
    clean_signal = photon_counts .- noise_level
    
    # Ensure no negative values (set to small positive value)
    clean_signal = max.(clean_signal, 1e-6)
    
    return clean_signal
end

"""
    process_noise_correction_csv(input_csv::String, output_csv::String="";
                                plot_results::Bool=true, save_plots::Bool=true) -> String

Process noise correction on CSV data and augment with new columns.
"""
function process_noise_correction_csv(input_csv::String, output_csv::String="";
                                     plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_noise_corrected.csv")
    end
    
    println("Processing noise correction for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["Photon_Counts", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV")
        end
    end
    
    # Extract data
    photon_counts = df.Photon_Counts
    altitude = df.Altitude_m
    
    # Estimate noise level
    noise_level, noise_indices = estimate_noise_level(photon_counts, altitude)
    
    # Apply noise correction
    clean_signal = apply_noise_correction(photon_counts, noise_level)
    
    # Add new columns to DataFrame
    df.Noise_Level = fill(noise_level, nrow(df))
    df.S_clean = clean_signal
    df.Noise_Indices = [i in noise_indices for i in 1:nrow(df)]
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add noise correction metadata
    push!(metadata_lines, "# Noise Correction Applied")
    push!(metadata_lines, "# Noise Level: $(noise_level)")
    push!(metadata_lines, "# Noise Altitude Range: $(NOISE_ALTITUDE_MIN) - $(NOISE_ALTITUDE_MAX) m")
    push!(metadata_lines, "# Noise Estimation Points: $(length(noise_indices))")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ Noise-corrected CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_noise_correction.png")
            mkpath("plots")
        end
        
        create_noise_correction_plot(photon_counts, clean_signal, altitude, noise_level, 
                                   noise_indices, plot_filename)
    end
    
    return output_csv
end

"""
    create_noise_correction_plot(raw_signal::Vector{Float64}, clean_signal::Vector{Float64},
                                altitude::Vector{Float64}, noise_level::Float64,
                                noise_indices::Vector{Int}, output_path::String="")

Create before/after comparison plot for noise correction.
"""
function create_noise_correction_plot(raw_signal::Vector{Float64}, clean_signal::Vector{Float64},
                                     altitude::Vector{Float64}, noise_level::Float64,
                                     noise_indices::Vector{Int}, output_path::String="")
    
    # Create before/after comparison plot
    p = plot(layout=(2,1), size=(800, 600))
    
    # Plot raw signal
    plot!(p[1], altitude/1000, raw_signal,
          title="Raw LIDAR Signal",
          xlabel="Altitude (km)",
          ylabel="Photon Counts",
          label="Raw Signal",
          linewidth=1,
          color=:red)
    
    # Add noise level line
    hline!(p[1], [noise_level],
           label="Noise Level",
           linestyle=:dash,
           color=:black,
           linewidth=2)
    
    # Highlight noise estimation region
    if !isempty(noise_indices)
        noise_alt_min = minimum(altitude[noise_indices]) / 1000
        noise_alt_max = maximum(altitude[noise_indices]) / 1000
        vspan!(p[1], [noise_alt_min, noise_alt_max],
               alpha=0.2,
               color=:gray,
               label="Noise Region")
    end
    
    # Plot clean signal
    plot!(p[2], altitude/1000, clean_signal,
          title="Noise-Corrected LIDAR Signal",
          xlabel="Altitude (km)",
          ylabel="Photon Counts",
          label="Clean Signal",
          linewidth=1,
          color=:blue)
    
    # Overall title
    plot!(p, plot_title="Noise Correction Analysis")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ Noise correction plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_noise_correction(input_dir::String="data", output_dir::String="data";
                                  plot_results::Bool=true) -> Vector{String}

Batch process noise correction for all CSV files in a directory.
"""
function batch_process_noise_correction(input_dir::String="data", output_dir::String="data";
                                       plot_results::Bool=true)
    
    println("="^50)
    println("BATCH NOISE CORRECTION PROCESSING")
    println("="^50)
    
    # Find all CSV files
    csv_files = filter(f -> endswith(f, ".csv") && !contains(f, "noise_corrected"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No CSV files found in $input_dir")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            base_name = splitext(basename(csv_file))[1]
            output_csv = joinpath(output_dir, "$(base_name)_noise_corrected.csv")
            
            processed_file = process_noise_correction_csv(csv_file, output_csv, 
                                                        plot_results=plot_results, 
                                                        save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("NOISE CORRECTION BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export estimate_noise_level, apply_noise_correction
export process_noise_correction_csv, create_noise_correction_plot
export batch_process_noise_correction
