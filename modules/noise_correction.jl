# Noise Correction Module - Simplified Version

using CSV
using DataFrames
using Statistics

"""
    estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64}) -> Float64

Estimate noise level from high altitude region (36-60 km).
"""
function estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64})
    noise_indices = findall(alt -> 36000.0 <= alt <= 60000.0, altitude)
    
    if isempty(noise_indices)
        println("⚠️  No data in noise region (36-60 km), using last 10% of data")
        noise_indices = max(1, round(Int, 0.9 * length(photon_counts))):length(photon_counts)
    end
    
    noise_level = mean(photon_counts[noise_indices])
    println("📊 Noise level estimated: $(noise_level:.2f) (from $(length(noise_indices)) points)")
    
    return noise_level
end

"""
    apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64) -> Vector{Float64}

Apply noise correction to photon counts.
"""
function apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64)
    corrected_counts = photon_counts .- noise_level
    corrected_counts = max.(corrected_counts, 0.0)  # Ensure non-negative
    
    return corrected_counts
end

"""
    process_noise_correction_csv(input_csv::String, output_csv::String="") -> String

Process noise correction for CSV file.
"""
function process_noise_correction_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_noise_corrected.csv")
    end
    
    println("Processing noise correction: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Counts" in names(df)) || !("Range_m" in names(df))
        error("Required columns 'Counts' or 'Range_m' not found")
    end
    
    photon_counts = Float64.(df.Counts)
    altitude = df.Range_m  # Using range as altitude for vertical pointing
    
    noise_level = estimate_noise_level(photon_counts, altitude)
    corrected_counts = apply_noise_correction(photon_counts, noise_level)
    
    # Add corrected data to DataFrame
    df.Noise_Level = fill(noise_level, nrow(df))
    df.Corrected_Counts = corrected_counts
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Noise corrected data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_noise_correction(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process noise correction for all CSV files.
"""
function batch_process_noise_correction(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing noise correction...")
    
    csv_files = filter(file -> endswith(file, ".csv") && !contains(file, "noise_corrected"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), ".csv" => "_noise_corrected.csv"))
            result_file = process_noise_correction_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export estimate_noise_level, apply_noise_correction
export process_noise_correction_csv, batch_process_noise_correction
