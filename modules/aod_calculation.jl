# Aerosol Optical Depth (AOD) Calculation Module
# Calculate AOD between altitude ranges with optional lidar ratio
# Augments CSV data with AOD analysis columns

using CSV
using DataFrames
using Statistics
using Plots

"""
    calculate_aod(beta_aerosol::Vector{Float64},
                 altitude::Vector{Float64},
                 h1::Float64,
                 h2::Float64;
                 lidar_ratio::Union{Float64, Nothing}=nothing) -> Float64

Calculate Aerosol Optical Depth between altitudes h1 and h2.
If lidar_ratio is provided: AOD = ∑[L × βₐ(z) × Δz]
Otherwise: AOD = ∑[βₐ(z) × Δz] (backscatter-related AOD)
"""
function calculate_aod(beta_aerosol::Vector{Float64},
                      altitude::Vector{Float64},
                      h1::Float64,
                      h2::Float64;
                      lidar_ratio::Union{Float64, Nothing}=nothing)

    if length(beta_aerosol) != length(altitude)
        error("Aerosol backscatter and altitude vectors must have the same length")
    end

    if h1 >= h2
        error("h1 must be less than h2")
    end

    # Find indices within the altitude range
    indices = findall(alt -> h1 <= alt <= h2, altitude)

    if length(indices) < 2
        @warn "Insufficient data points in altitude range [$h1, $h2] m"
        return 0.0
    end

    # Calculate AOD using trapezoidal integration
    aod = 0.0
    for i in 1:(length(indices)-1)
        idx1 = indices[i]
        idx2 = indices[i+1]

        # Altitude difference
        dz = altitude[idx2] - altitude[idx1]

        # Average backscatter in this layer
        avg_beta = (beta_aerosol[idx1] + beta_aerosol[idx2]) / 2

        # Add contribution to AOD
        if lidar_ratio !== nothing
            aod += lidar_ratio * avg_beta * dz
        else
            aod += avg_beta * dz
        end
    end

    return aod
end

"""
    calculate_aod_profile(beta_aerosol::Vector{Float64},
                         altitude::Vector{Float64};
                         lidar_ratio::Union{Float64, Nothing}=nothing) -> Vector{Float64}

Calculate cumulative AOD profile from ground up.
"""
function calculate_aod_profile(beta_aerosol::Vector{Float64},
                              altitude::Vector{Float64};
                              lidar_ratio::Union{Float64, Nothing}=nothing)

    n_points = length(beta_aerosol)
    aod_profile = zeros(n_points)

    # Calculate cumulative AOD
    for i in 2:n_points
        dz = altitude[i] - altitude[i-1]
        avg_beta = (beta_aerosol[i-1] + beta_aerosol[i]) / 2

        if lidar_ratio !== nothing
            aod_profile[i] = aod_profile[i-1] + lidar_ratio * avg_beta * dz
        else
            aod_profile[i] = aod_profile[i-1] + avg_beta * dz
        end
    end

    return aod_profile
end

"""
    process_aod_analysis_csv(input_csv::String, output_csv::String="";
                            altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                            lidar_ratio::Union{Float64, Nothing}=nothing,
                            plot_results::Bool=true, save_plots::Bool=true) -> String

Process AOD analysis from CSV file with backscatter data.
Augments CSV with AOD profile and range-specific AOD values.
"""
function process_aod_analysis_csv(input_csv::String, output_csv::String="";
                                 altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                                 lidar_ratio::Union{Float64, Nothing}=nothing,
                                 plot_results::Bool=true, save_plots::Bool=true)
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = replace(basename(input_csv), ".csv" => "")
        output_dir = dirname(input_csv)
        output_csv = joinpath(output_dir, "$(base_name)_aod.csv")
    end
    
    println("Processing AOD analysis for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["Beta_aerosol", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV. Run backscatter analysis first.")
        end
    end
    
    # Extract data
    beta_aerosol = df.Beta_aerosol
    altitude = df.Altitude_m
    
    # Calculate AOD profile
    aod_profile = calculate_aod_profile(beta_aerosol, altitude, lidar_ratio=lidar_ratio)
    
    # Calculate AOD for specific altitude ranges
    aod_values = Dict{String, Float64}()
    
    println("Aerosol Optical Depth (AOD) Analysis:")
    if lidar_ratio !== nothing
        println("  Using lidar ratio: $(lidar_ratio) sr")
    else
        println("  Backscatter-related AOD (no lidar ratio)")
    end
    
    for (h1, h2) in altitude_ranges
        aod_val = calculate_aod(beta_aerosol, altitude, h1, h2, lidar_ratio=lidar_ratio)
        range_key = "AOD_$(Int(h1/1000))km_$(Int(h2/1000))km"
        aod_values[range_key] = aod_val
        println("  AOD ($(Int(h1/1000))-$(Int(h2/1000))km): $(aod_val:.6f)")
    end
    
    # Total column AOD
    total_aod = aod_profile[end]
    aod_values["AOD_total"] = total_aod
    println("  Total column AOD: $(total_aod:.6f)")
    
    # Add AOD data to DataFrame
    df_output = copy(df)
    df_output.AOD_profile = aod_profile
    
    # Add range-specific AOD values as constant columns
    for (key, value) in aod_values
        df_output[!, key] = fill(value, nrow(df_output))
    end
    
    # Save augmented CSV
    mkpath(dirname(output_csv))
    
    # Read original metadata
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Write output with metadata
    open(output_csv, "w") do file
        # Write original metadata
        for line in metadata_lines
            write(file, line * "\n")
        end
        
        # Add AOD processing metadata
        write(file, "# AOD Analysis Applied\n")
        if lidar_ratio !== nothing
            write(file, "# Lidar Ratio: $(lidar_ratio) sr\n")
        else
            write(file, "# Backscatter-related AOD (no lidar ratio)\n")
        end
        write(file, "# Total Column AOD: $(total_aod:.6f)\n")
        for (key, value) in aod_values
            if key != "AOD_total"
                write(file, "# $key: $(value:.6f)\n")
            end
        end
        write(file, "#\n")
    end
    
    # Append DataFrame
    CSV.write(output_csv, df_output, append=true)
    
    println("✅ AOD analysis complete: $(basename(output_csv))")
    
    # Create plots if requested
    if plot_results
        create_aod_analysis_plot(altitude, aod_profile, beta_aerosol, aod_values, 
                                altitude_ranges, lidar_ratio, 
                                basename(input_csv), save_plots)
    end
    
    return output_csv
end

"""
    create_aod_analysis_plot(altitude::Vector{Float64}, aod_profile::Vector{Float64},
                           beta_aerosol::Vector{Float64}, aod_values::Dict{String, Float64},
                           altitude_ranges::Vector{Tuple{Float64, Float64}},
                           lidar_ratio::Union{Float64, Nothing}, filename::String,
                           save_plots::Bool=true) -> Plots.Plot

Create comprehensive AOD analysis visualization.
"""
function create_aod_analysis_plot(altitude::Vector{Float64}, aod_profile::Vector{Float64},
                                 beta_aerosol::Vector{Float64}, aod_values::Dict{String, Float64},
                                 altitude_ranges::Vector{Tuple{Float64, Float64}},
                                 lidar_ratio::Union{Float64, Nothing}, filename::String,
                                 save_plots::Bool=true)
    
    # Create two-panel plot
    p = plot(layout=(1,2), size=(1000, 400))
    
    # Panel 1: AOD profile
    plot!(p[1], aod_profile, altitude/1000,
          title="Cumulative AOD Profile",
          xlabel="Cumulative AOD",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:red,
          label="AOD Profile")
    
    # Add horizontal lines for altitude ranges
    for (i, (h1, h2)) in enumerate(altitude_ranges)
        hline!(p[1], [h2/1000],
               label="$(Int(h2/1000)) km",
               linestyle=:dash,
               color=palette(:default)[i+1],
               linewidth=1)
    end
    
    # Panel 2: Aerosol backscatter profile
    plot!(p[2], beta_aerosol.*1e6, altitude/1000,
          title="Aerosol Backscatter",
          xlabel="βₐ (10⁻⁶ m⁻¹ sr⁻¹)",
          ylabel="Altitude (km)",
          linewidth=2,
          color=:green,
          label="Aerosol Backscatter")
    
    # Overall title
    title_suffix = lidar_ratio !== nothing ? " (L=$(lidar_ratio) sr)" : " (Backscatter-related)"
    plot!(p, plot_title="AOD Analysis - $filename$title_suffix")
    
    if save_plots
        mkpath("plots")
        plot_filename = "plots/aod_analysis_$(replace(filename, ".csv" => "")).png"
        savefig(p, plot_filename)
        println("📊 AOD analysis plot saved: $plot_filename")
    end
    
    return p
end

"""
    batch_process_aod_analysis(input_dir::String, output_dir::String="";
                              altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                              lidar_ratio::Union{Float64, Nothing}=nothing,
                              plot_results::Bool=true) -> Vector{String}

Batch process AOD analysis for all backscatter CSV files in a directory.
"""
function batch_process_aod_analysis(input_dir::String, output_dir::String="";
                                   altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                                   lidar_ratio::Union{Float64, Nothing}=nothing,
                                   plot_results::Bool=true)
    
    if isempty(output_dir)
        output_dir = input_dir
    end
    
    # Find all backscatter CSV files
    csv_files = String[]
    for file in readdir(input_dir, join=true)
        if endswith(file, "_backscatter.csv") || (endswith(file, ".csv") && occursin("backscatter", file))
            push!(csv_files, file)
        end
    end
    
    if isempty(csv_files)
        println("⚠️  No backscatter CSV files found in $input_dir")
        return String[]
    end
    
    println("🔄 Batch processing AOD analysis for $(length(csv_files)) files...")
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\n[$i/$(length(csv_files))] Processing: $(basename(csv_file))")
            
            # Generate output filename
            base_name = replace(basename(csv_file), ".csv" => "")
            output_csv = joinpath(output_dir, "$(base_name)_aod.csv")
            
            # Process AOD analysis
            result_file = process_aod_analysis_csv(csv_file, output_csv,
                                                  altitude_ranges=altitude_ranges,
                                                  lidar_ratio=lidar_ratio,
                                                  plot_results=plot_results,
                                                  save_plots=true)
            
            push!(processed_files, result_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n✅ AOD batch processing complete!")
    println("📁 Processed $(length(processed_files)) files")
    println("📊 Results saved in: $output_dir")
    
    return processed_files
end

# Export main functions
export calculate_aod, calculate_aod_profile
export process_aod_analysis_csv, create_aod_analysis_plot
export batch_process_aod_analysis
