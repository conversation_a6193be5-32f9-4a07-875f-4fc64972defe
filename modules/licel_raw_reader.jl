# LICEL Raw Data Reader Module - Simplified Version
# Matches pre-processing codes exactly

using CSV
using DataFrames
using Dates
using Statistics

# Simple data structure
struct LidarData
    filename::String
    raw_counts::Vector{UInt32}
    range::Vector{Float64}
end

"""
    read_licel_raw_file(filepath::String) -> LidarData

Read LICEL raw file exactly like pre-processing code.
"""
function read_licel_raw_file(filepath::String)
    if !isfile(filepath)
        error("File not found: $filepath")
    end
    
    raw_counts = UInt32[]
    
    open(filepath, "r") do io
        # Skip 3 header lines (matching pre-processing code)
        for _ in 1:3
            if !eof(io)
                readline(io)
            end
        end

        # Read binary UInt32 data
        while !eof(io)
            try
                push!(raw_counts, read(io, UInt32))
            catch
                break
            end
        end
    end
    
    # Calculate range: range = 15 + (i-1) * 30 (matching RTI_Plotting)
    range = [15.0 + (i-1) * 30.0 for i in 1:length(raw_counts)]
    
    return LidarData(basename(filepath), raw_counts, range)
end

"""
    save_lidar_csv(data::LidarData, output_path::String)

Save LIDAR data as simple CSV.
"""
function save_lidar_csv(data::LidarData, output_path::String)
    df = DataFrame(
        Index = 1:length(data.raw_counts),
        Counts = data.raw_counts,
        Range_m = data.range
    )
    
    mkpath(dirname(output_path))
    CSV.write(output_path, df)
    println("✅ CSV saved: $output_path")
end

"""
    find_licel_files(directory::String) -> Vector{String}

Find LICEL files in directory.
"""
function find_licel_files(directory::String)
    if !isdir(directory)
        return String[]
    end
    
    files = String[]
    for item in readdir(directory, join=true)
        if isfile(item)
            filename = basename(item)
            if startswith(filename, "I") && length(split(filename, ".")) == 2
                push!(files, item)
            end
        end
    end
    
    return sort(files)
end

"""
    process_clear_sky_folder(input_dir::String, output_dir::String="data") -> Vector{String}

Process all LICEL files in folder.
"""
function process_clear_sky_folder(input_dir::String, output_dir::String="data")
    println("Processing folder: $input_dir")
    
    licel_files = find_licel_files(input_dir)
    
    if isempty(licel_files)
        println("⚠️  No LICEL files found")
        return String[]
    end
    
    println("Found $(length(licel_files)) LICEL files")
    
    processed_files = String[]
    
    for (i, file_path) in enumerate(licel_files)
        try
            println("Processing file $i/$(length(licel_files)): $(basename(file_path))")
            
            data = read_licel_raw_file(file_path)
            
            folder_name = basename(input_dir)
            base_name = basename(file_path)
            output_filename = "$(folder_name)_$(base_name).csv"
            output_path = joinpath(output_dir, output_filename)
            
            save_lidar_csv(data, output_path)
            push!(processed_files, output_path)
            
        catch e
            println("❌ Error processing $(basename(file_path)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

"""
    batch_process_clear_sky_data() -> Vector{String}

Process all clear sky folders.
"""
function batch_process_clear_sky_data()
    println("="^60)
    println("BATCH PROCESSING CLEAR SKY CBL DATA")
    println("="^60)

    clear_sky_folders = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
        "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky",
        "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
    ]

    all_processed_files = String[]

    for folder in clear_sky_folders
        if isdir(folder)
            println("\n" * "="^40)
            processed_files = process_clear_sky_folder(folder, "data")
            append!(all_processed_files, processed_files)
        else
            println("⚠️  Folder not found: $folder")
        end
    end

    println("\n" * "="^60)
    println("BATCH PROCESSING COMPLETE")
    println("Total files processed: $(length(all_processed_files))")
    println("="^60)

    return all_processed_files
end

# Export functions
export LidarData
export read_licel_raw_file, save_lidar_csv
export find_licel_files, process_clear_sky_folder, batch_process_clear_sky_data
