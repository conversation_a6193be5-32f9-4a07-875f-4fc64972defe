# Range Correction Module
# Apply 1/z² range correction with 30m resolution bins
# Augments CSV data with range-corrected signal columns

using CSV
using DataFrames
using Plots

# Configuration constants
const RANGE_RESOLUTION = 30.0  # meters

"""
    apply_range_correction(clean_signal::Vector{Float64}, range::Vector{Float64}) -> Vector{Float64}

Apply range correction to compensate for 1/z² signal attenuation.
X²(z) = S_clean(z) × z²
"""
function apply_range_correction(clean_signal::Vector{Float64}, range::Vector{Float64})
    if length(clean_signal) != length(range)
        error("Signal and range vectors must have the same length")
    end
    
    # Apply range correction: multiply by range squared
    range_corrected = clean_signal .* (range .^ 2)
    
    return range_corrected
end

"""
    calculate_range_statistics(range_corrected::Vector{Float64}, range::Vector{Float64}) -> Dict{String, Float64}

Calculate statistics for range correction validation.
"""
function calculate_range_statistics(range_corrected::Vector{Float64}, range::Vector{Float64})
    stats = Dict{String, Float64}()
    
    stats["max_range_corrected"] = maximum(range_corrected)
    stats["min_range_corrected"] = minimum(range_corrected)
    stats["mean_range_corrected"] = mean(range_corrected)
    stats["std_range_corrected"] = std(range_corrected)
    stats["range_min"] = minimum(range)
    stats["range_max"] = maximum(range)
    stats["range_resolution"] = RANGE_RESOLUTION
    
    return stats
end

"""
    process_range_correction_csv(input_csv::String, output_csv::String="";
                                plot_results::Bool=true, save_plots::Bool=true) -> String

Process range correction on CSV data and augment with new columns.
"""
function process_range_correction_csv(input_csv::String, output_csv::String="";
                                     plot_results::Bool=true, save_plots::Bool=true)
    
    if !isfile(input_csv)
        error("Input CSV file not found: $input_csv")
    end
    
    # Generate output filename if not provided
    if isempty(output_csv)
        base_name = splitext(basename(input_csv))[1]
        output_csv = joinpath(dirname(input_csv), "$(base_name)_range_corrected.csv")
    end
    
    println("Processing range correction for: $(basename(input_csv))")
    
    # Read CSV data
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Validate required columns
    required_cols = ["S_clean", "Range_m", "Altitude_m"]
    for col in required_cols
        if !(col in names(df))
            error("Required column '$col' not found in CSV. Run noise correction first.")
        end
    end
    
    # Extract data
    clean_signal = df.S_clean
    range = df.Range_m
    altitude = df.Altitude_m
    
    # Apply range correction
    range_corrected = apply_range_correction(clean_signal, range)
    
    # Calculate statistics
    stats = calculate_range_statistics(range_corrected, range)
    
    println("Range correction applied:")
    println("  Range resolution: $(stats["range_resolution"]) m")
    println("  Range: $(stats["range_min"]:.1f) - $(stats["range_max"]:.1f) m")
    println("  Max range-corrected signal: $(stats["max_range_corrected"]:.2e)")
    println("  Enhancement factor: $(stats["max_range_corrected"] / maximum(clean_signal):.1f)")
    
    # Add new columns to DataFrame
    df.X2 = range_corrected
    df.Range_Enhancement_Factor = range_corrected ./ clean_signal
    
    # Copy metadata from input file
    metadata_lines = String[]
    open(input_csv, "r") do file
        for line in eachline(file)
            if startswith(line, "#")
                push!(metadata_lines, line)
            else
                break
            end
        end
    end
    
    # Add range correction metadata
    push!(metadata_lines, "# Range Correction Applied")
    push!(metadata_lines, "# Range Resolution: $(RANGE_RESOLUTION) m")
    push!(metadata_lines, "# Max Range-Corrected Signal: $(stats["max_range_corrected"]:.2e)")
    push!(metadata_lines, "# Range: $(stats["range_min"]:.1f) - $(stats["range_max"]:.1f) m")
    push!(metadata_lines, "#")
    
    # Write output CSV with metadata
    mkpath(dirname(output_csv))
    open(output_csv, "w") do file
        for line in metadata_lines
            write(file, line * "\n")
        end
    end
    
    # Append CSV data
    CSV.write(output_csv, df, append=true)
    println("✅ Range-corrected CSV saved: $output_csv")
    
    # Generate plots if requested
    if plot_results
        plot_filename = ""
        if save_plots
            base_name = splitext(basename(input_csv))[1]
            plot_filename = joinpath("plots", "$(base_name)_range_correction.png")
            mkpath("plots")
        end
        
        create_range_correction_plot(clean_signal, range_corrected, altitude, plot_filename)
    end
    
    return output_csv
end

"""
    create_range_correction_plot(clean_signal::Vector{Float64}, range_corrected::Vector{Float64},
                                altitude::Vector{Float64}, output_path::String="")

Create before/after comparison plot for range correction.
"""
function create_range_correction_plot(clean_signal::Vector{Float64}, range_corrected::Vector{Float64},
                                     altitude::Vector{Float64}, output_path::String="")
    
    # Create comparison plot
    p = plot(layout=(2,1), size=(800, 600))
    
    # Plot clean signal
    plot!(p[1], altitude/1000, clean_signal,
          title="Noise-Corrected Signal",
          xlabel="Altitude (km)",
          ylabel="Signal Intensity",
          label="Clean Signal",
          linewidth=1,
          color=:blue)
    
    # Plot range-corrected signal
    plot!(p[2], altitude/1000, range_corrected,
          title="Range-Corrected Signal (X²)",
          xlabel="Altitude (km)",
          ylabel="Signal × Range²",
          label="Range Corrected",
          linewidth=1,
          color=:green)
    
    # Overall title
    plot!(p, plot_title="Range Correction Analysis")
    
    if !isempty(output_path)
        savefig(p, output_path)
        println("✅ Range correction plot saved: $output_path")
    end
    
    display(p)
    return p
end

"""
    batch_process_range_correction(input_dir::String="data", output_dir::String="data";
                                  plot_results::Bool=true) -> Vector{String}

Batch process range correction for all noise-corrected CSV files in a directory.
"""
function batch_process_range_correction(input_dir::String="data", output_dir::String="data";
                                       plot_results::Bool=true)
    
    println("="^50)
    println("BATCH RANGE CORRECTION PROCESSING")
    println("="^50)
    
    # Find all noise-corrected CSV files
    csv_files = filter(f -> endswith(f, "noise_corrected.csv") && !contains(f, "range_corrected"), 
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No noise-corrected CSV files found in $input_dir")
        println("    Run noise correction first.")
        return String[]
    end
    
    processed_files = String[]
    
    for (i, csv_file) in enumerate(csv_files)
        try
            println("\nProcessing file $i/$(length(csv_files)): $(basename(csv_file))")
            
            base_name = replace(splitext(basename(csv_file))[1], "_noise_corrected" => "")
            output_csv = joinpath(output_dir, "$(base_name)_range_corrected.csv")
            
            processed_file = process_range_correction_csv(csv_file, output_csv, 
                                                        plot_results=plot_results, 
                                                        save_plots=true)
            push!(processed_files, processed_file)
            
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("\n" * "="^50)
    println("RANGE CORRECTION BATCH COMPLETE")
    println("Processed $(length(processed_files)) files")
    println("="^50)
    
    return processed_files
end

# Export main functions
export apply_range_correction, calculate_range_statistics
export process_range_correction_csv, create_range_correction_plot
export batch_process_range_correction
