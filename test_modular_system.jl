# Test Script for Modular LIDAR Processing System
# Verifies that all modules work correctly with UInt32 photon count handling

include("lidar_processing_pipeline.jl")

println("🧪 TESTING MODULAR LIDAR PROCESSING SYSTEM")
println("="^60)

# Test 1: Check if modules load correctly
println("\n1️⃣  MODULE LOADING TEST")
println("-"^30)

modules_to_test = [
    "modules/licel_raw_reader.jl",
    "modules/noise_correction.jl",
    "modules/range_correction.jl",
    "modules/normalization.jl",
    "modules/molecular_backscatter.jl",
    "modules/backscatter_analysis.jl",
    "modules/asr_analysis.jl",
    "modules/aod_calculation.jl",
    "modules/rtdi_generator.jl"
]

for module_file in modules_to_test
    if isfile(module_file)
        println("✅ $module_file")
    else
        println("❌ $module_file - NOT FOUND")
    end
end

# Test 2: Directory structure
println("\n2️⃣  DIRECTORY STRUCTURE TEST")
println("-"^30)

setup_directories()

required_dirs = ["data", "plots", "results", "modules"]
for dir in required_dirs
    if isdir(dir)
        println("✅ $dir/")
    else
        println("❌ $dir/ - NOT FOUND")
    end
end

# Test 3: Look for sample data
println("\n3️⃣  SAMPLE DATA AVAILABILITY")
println("-"^30)

sample_locations = [
    "clear sky CBL Aratrika/08May2023_CBL_clearsky",
    "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
    "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky", 
    "clear sky CBL Aratrika/18May2023_CBL_study_clearsky",
    "processed/raw"
]

available_data = false
for location in sample_locations
    if isdir(location)
        files = find_licel_files(location)
        if !isempty(files)
            println("✅ $location ($(length(files)) files)")
            available_data = true
        else
            println("⚠️  $location (no LICEL files)")
        end
    else
        println("❌ $location (not found)")
    end
end

# Test 4: Function availability
println("\n4️⃣  FUNCTION AVAILABILITY TEST")
println("-"^30)

functions_to_test = [
    "read_licel_raw_file",
    "check_overflow_values",
    "save_lidar_csv",
    "process_noise_correction_csv",
    "process_range_correction_csv",
    "process_normalization_csv",
    "process_molecular_backscatter_csv",
    "process_backscatter_analysis_csv",
    "process_asr_analysis_csv",
    "process_aod_analysis_csv",
    "create_rtdi_map_from_csv",
    "batch_generate_rtdi_maps"
]

for func_name in functions_to_test
    try
        func = eval(Symbol(func_name))
        println("✅ $func_name")
    catch
        println("❌ $func_name - NOT AVAILABLE")
    end
end

# Test 5: Quick processing test (if data available)
if available_data
    println("\n5️⃣  QUICK PROCESSING TEST")
    println("-"^30)
    
    # Find a sample file
    sample_file = ""
    for location in sample_locations
        if isdir(location)
            files = find_licel_files(location)
            if !isempty(files)
                sample_file = files[1]
                break
            end
        end
    end
    
    if !isempty(sample_file)
        try
            println("Testing with: $(basename(sample_file))")
            
            # Test raw file reading
            println("  📥 Reading raw file...")
            data = read_licel_raw_file(sample_file)
            println("    ✅ $(length(data.raw_counts)) data points loaded")
            
            # Test overflow check
            println("  🛡️  Checking overflow...")
            overflow_analysis = check_overflow_values(data.raw_counts)
            println("    ✅ Overflow check complete")
            
            # Test CSV saving
            println("  💾 Testing CSV save...")
            test_csv = "data/test_sample.csv"
            save_lidar_csv(data, test_csv)
            
            if isfile(test_csv)
                println("    ✅ CSV saved successfully")
                
                # Test reading CSV back
                df = CSV.read(test_csv, DataFrame, comment="#")
                println("    ✅ CSV read back: $(nrow(df)) rows, $(ncol(df)) columns")
            else
                println("    ❌ CSV save failed")
            end
            
        catch e
            println("    ❌ Processing test failed: $e")
        end
    else
        println("  ⚠️  No sample file found for testing")
    end
else
    println("\n5️⃣  QUICK PROCESSING TEST - SKIPPED")
    println("-"^30)
    println("  ⚠️  No sample data available")
end

# Summary
println("\n" * "="^60)
println("🏁 MODULAR SYSTEM TEST SUMMARY")
println("="^60)

if available_data
    println("✅ System appears ready for processing")
    println("\n🚀 NEXT STEPS:")
    println("  1. demo_single_file()           # Process one file (all stages)")
    println("  2. run_complete_pipeline()      # Process all data (includes ASR, AOD, RTDI)")
    println("  3. batch_process_clear_sky_data() # Raw conversion only")
    println("\n🔬 ADVANCED ANALYSIS:")
    println("  • ASR analysis: process_asr_analysis_csv()")
    println("  • AOD calculation: process_aod_analysis_csv()")
    println("  • RTDI maps: generate_rtdi_maps_for_day()")
else
    println("⚠️  System loaded but no sample data found")
    println("\n📁 TO GET STARTED:")
    println("  1. Ensure LICEL files are in clear sky folders")
    println("  2. Run batch_process_clear_sky_data() to convert raw files")
    println("  3. Then run individual processing stages")
    println("  4. Generate RTDI maps and perform ASR/AOD analysis")
end

println("\n📚 HELP:")
println("  show_pipeline_help()            # Show detailed help")
println("="^60)
